/**
 * 工价配置API接口
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 16:35:00 +08:00; Reason: Task-006 创建前端WageConfig API接口, 定义TypeScript接口和HTTP请求方法; Principle_Applied: 前端API封装;}}
 */

import { http } from './request'

// 激励工价项目接口
export interface IncentiveWageItem {
  cumulative: number  // 累计针数
  reward: number      // 奖励金额
}

// 工价类型枚举
export enum WageType {
  PATTERN_WAGE = 1,      // 花样工价
  CHANGE_PIECE_WAGE = 2, // 换片单价
  CHANGE_LINE_WAGE = 3   // 换线单价
}

// 工价配置接口
export interface WageConfig {
  id: number
  enterpriseId: number
  createdUserId?: number
  updatedUserId?: number
  patternId?: number
  patternWage?: number
  incentiveWage?: IncentiveWageItem[]
  type: WageType
  changePieceWage?: number
  changeLineWage?: number
  createdAt: string
  updatedAt: string
  // 关联数据
  pattern?: {
    id: number
    name: string
    code?: string
  }
  createdUser?: {
    id: number
    name: string
  }
  updatedUser?: {
    id: number
    name: string
  }
  enterprise?: {
    id: number
    name: string
  }
}

// 工价配置列表查询参数
export interface WageConfigListQuery {
  page?: number
  pageSize?: number
  search?: string
  type?: WageType
  patternId?: number
  createdUserId?: number
}

// 工价配置列表响应
export interface WageConfigListResponse {
  wageConfigs: WageConfig[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 工价配置搜索选项
export interface WageConfigSearchOptions {
  patterns: Array<{ id: number; name: string; code?: string }>
  createdUsers: Array<{ id: number; name: string }>
  types: Array<{ label: string; value: WageType }>
}

// 创建工价配置请求
export interface CreateWageConfigRequest {
  type: WageType
  patternId?: number
  patternWage?: number
  incentiveWage?: IncentiveWageItem[]
  changePieceWage?: number
  changeLineWage?: number
}

// 更新工价配置请求
export interface UpdateWageConfigRequest extends Partial<CreateWageConfigRequest> {}

/**
 * 工价配置管理API
 */
export const wageConfigApi = {
  /**
   * 获取工价配置列表
   * @param params 查询参数
   * @returns 工价配置列表响应
   */
  getWageConfigList(params: WageConfigListQuery): Promise<WageConfigListResponse> {
    return http.get('/wage-configs', { params })
  },

  /**
   * 获取工价配置详情
   * @param id 工价配置ID
   * @returns 工价配置详情
   */
  getWageConfigById(id: number): Promise<WageConfig> {
    return http.get(`/wage-configs/${id}`)
  },

  /**
   * 创建工价配置
   * @param data 创建请求数据
   * @returns 创建的工价配置
   */
  createWageConfig(data: CreateWageConfigRequest): Promise<WageConfig> {
    return http.post('/wage-configs', data)
  },

  /**
   * 更新工价配置
   * @param id 工价配置ID
   * @param data 更新请求数据
   * @returns 更新后的工价配置
   */
  updateWageConfig(id: number, data: UpdateWageConfigRequest): Promise<WageConfig> {
    return http.put(`/wage-configs/${id}`, data)
  },

  /**
   * 删除工价配置
   * @param id 工价配置ID
   * @returns 删除结果
   */
  deleteWageConfig(id: number): Promise<void> {
    return http.delete(`/wage-configs/${id}`)
  },

  /**
   * 获取工价配置搜索选项
   * @returns 搜索选项数据
   */
  getSearchOptions(): Promise<WageConfigSearchOptions> {
    return http.get('/wage-configs/search-options')
  }
}

/**
 * 工价类型标签映射
 */
export const WageTypeLabels: Record<WageType, string> = {
  [WageType.PATTERN_WAGE]: '花样工价',
  [WageType.CHANGE_PIECE_WAGE]: '换片单价',
  [WageType.CHANGE_LINE_WAGE]: '换线单价'
}

/**
 * 工价类型选项
 */
export const WageTypeOptions = [
  { label: '花样工价', value: WageType.PATTERN_WAGE },
  { label: '换片单价', value: WageType.CHANGE_PIECE_WAGE },
  { label: '换线单价', value: WageType.CHANGE_LINE_WAGE }
]

/**
 * 格式化激励工价显示
 * @param incentiveWage 激励工价数组
 * @returns 格式化后的字符串
 */
export function formatIncentiveWage(incentiveWage?: IncentiveWageItem[]): string {
  if (!incentiveWage || incentiveWage.length === 0) {
    return '无'
  }
  
  return incentiveWage
    .map(item => `${item.cumulative}针奖励¥${item.reward}`)
    .join('，')
}

/**
 * 验证激励工价数据
 * @param incentiveWage 激励工价数组
 * @returns 验证结果
 */
export function validateIncentiveWage(incentiveWage: IncentiveWageItem[]): {
  valid: boolean
  message?: string
} {
  if (!Array.isArray(incentiveWage)) {
    return { valid: false, message: '激励工价必须是数组格式' }
  }

  if (incentiveWage.length === 0) {
    return { valid: true }
  }

  for (let i = 0; i < incentiveWage.length; i++) {
    const item = incentiveWage[i]
    
    if (!item.cumulative || !item.reward) {
      return { valid: false, message: `第${i + 1}项必须包含累计针数和奖励金额` }
    }

    if (typeof item.cumulative !== 'number' || typeof item.reward !== 'number') {
      return { valid: false, message: `第${i + 1}项的累计针数和奖励金额必须是数字` }
    }

    if (item.cumulative <= 0 || item.reward <= 0) {
      return { valid: false, message: `第${i + 1}项的累计针数和奖励金额必须大于0` }
    }

    // 检查累计针数是否递增
    if (i > 0 && item.cumulative <= incentiveWage[i - 1].cumulative) {
      return { valid: false, message: `第${i + 1}项的累计针数必须大于前一项` }
    }
  }

  return { valid: true }
}

/**
 * 创建默认的激励工价项目
 * @returns 默认激励工价项目
 */
export function createDefaultIncentiveWageItem(): IncentiveWageItem {
  return {
    cumulative: 0,
    reward: 0
  }
}
