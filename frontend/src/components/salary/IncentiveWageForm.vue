<!--
  激励工价配置组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-27 17:00:00 +08:00; Reason: Task-008 创建激励工价配置组件, 处理动态表单的添加、删除和验证; Principle_Applied: 组件化设计;}}
-->

<template>
  <div class="incentive-wage-form">
    <div class="form-header">
      <div class="header-content">
        <h4>激励工价配置</h4>
        <span class="description">设置累计针数达到指定值时的奖励金额</span>
      </div>
      <div class="header-actions">
        <a-button 
          type="dashed" 
          size="small" 
          :icon="h(PlusOutlined)"
          :disabled="disabled"
          @click="addRule"
        >
          添加规则
        </a-button>
        <a-dropdown v-if="!disabled">
          <a-button size="small" :icon="h(MoreOutlined)">
            更多
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="template" @click="showTemplateModal">
                <template-outlined />
                使用模板
              </a-menu-item>
              <a-menu-item key="import" @click="showImportModal">
                <import-outlined />
                批量导入
              </a-menu-item>
              <a-menu-item key="export" @click="exportRules" :disabled="!hasRules">
                <export-outlined />
                导出配置
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="clear" @click="clearAllRules" :disabled="!hasRules" danger>
                <delete-outlined />
                清空所有
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>

    <!-- 规则列表 -->
    <div v-if="hasRules" class="rules-container">
      <div class="rules-header">
        <div class="col-cumulative">累计针数</div>
        <div class="col-reward">奖励金额</div>
        <div class="col-actions">操作</div>
      </div>
      
      <div class="rules-list">
        <div 
          v-for="(rule, index) in localRules" 
          :key="index" 
          class="rule-item"
          :class="{ 'rule-error': ruleErrors[index] }"
        >
          <div class="col-cumulative">
            <a-input-number
              v-model:value="rule.cumulative"
              :min="1"
              :max="999999999"
              :precision="0"
              placeholder="累计针数"
              :disabled="disabled"
              @change="handleRuleChange(index)"
              @blur="validateRule(index)"
            />
            <span class="unit">针</span>
          </div>
          
          <div class="col-reward">
            <span class="reward-prefix">奖励</span>
            <a-input-number
              v-model:value="rule.reward"
              :min="0"
              :precision="2"
              placeholder="奖励金额"
              :disabled="disabled"
              @change="handleRuleChange(index)"
              @blur="validateRule(index)"
            />
            <span class="unit">元</span>
          </div>
          
          <div class="col-actions">
            <a-button 
              type="text" 
              size="small"
              danger
              :disabled="disabled"
              :icon="h(DeleteOutlined)"
              @click="removeRule(index)"
              title="删除规则"
            />
          </div>
          
          <!-- 错误提示 -->
          <div v-if="ruleErrors[index]" class="rule-error-message">
            {{ ruleErrors[index] }}
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <a-empty description="暂无激励工价配置">
        <template #image>
          <gift-outlined style="font-size: 48px; color: #d9d9d9;" />
        </template>
        <a-button 
          v-if="!disabled"
          type="primary" 
          :icon="h(PlusOutlined)"
          @click="addRule"
        >
          添加第一条规则
        </a-button>
      </a-empty>
    </div>

    <!-- 规则统计 -->
    <div v-if="hasRules" class="rules-summary">
      <a-space>
        <span class="summary-item">
          <span class="label">规则数量：</span>
          <span class="value">{{ localRules.length }}</span>
        </span>
        <span class="summary-item">
          <span class="label">最高奖励：</span>
          <span class="value">¥{{ maxReward }}</span>
        </span>
        <span class="summary-item">
          <span class="label">总奖励：</span>
          <span class="value">¥{{ totalReward }}</span>
        </span>
      </a-space>
    </div>

    <!-- 模板选择模态框 -->
    <a-modal
      v-model:open="templateModalVisible"
      title="选择激励工价模板"
      @ok="applyTemplate"
      @cancel="templateModalVisible = false"
    >
      <div class="template-list">
        <div 
          v-for="template in templates" 
          :key="template.id"
          class="template-item"
          :class="{ 'template-selected': selectedTemplate === template.id }"
          @click="selectedTemplate = template.id"
        >
          <div class="template-name">{{ template.name }}</div>
          <div class="template-description">{{ template.description }}</div>
          <div class="template-rules">
            <span v-for="(rule, index) in template.rules" :key="index" class="template-rule">
              {{ rule.cumulative }}针→¥{{ rule.reward }}
            </span>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 批量导入模态框 -->
    <a-modal
      v-model:open="importModalVisible"
      title="批量导入激励工价"
      @ok="importRules"
      @cancel="importModalVisible = false"
    >
      <div class="import-content">
        <div class="import-description">
          请按照以下格式输入激励工价规则，每行一条：<br>
          <code>累计针数,奖励金额</code>
        </div>
        <a-textarea
          v-model:value="importText"
          placeholder="例如：&#10;10000,50&#10;50000,200&#10;100000,500"
          :rows="6"
          class="import-textarea"
        />
        <div class="import-example">
          <strong>示例：</strong><br>
          10000,50 （累计1万针奖励50元）<br>
          50000,200 （累计5万针奖励200元）<br>
          100000,500 （累计10万针奖励500元）
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  DeleteOutlined,
  MoreOutlined,
  FileTextOutlined as TemplateOutlined,
  UploadOutlined as ImportOutlined,
  DownloadOutlined as ExportOutlined,
  GiftOutlined
} from '@ant-design/icons-vue'
import type { IncentiveWageItem } from '../../api/wageConfig'

// 组件属性
interface Props {
  modelValue?: IncentiveWageItem[]
  disabled?: boolean
  maxRules?: number
}

// 组件事件
interface Emits {
  (e: 'update:modelValue', value: IncentiveWageItem[]): void
  (e: 'change', value: IncentiveWageItem[]): void
  (e: 'validate', valid: boolean, errors: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  disabled: false,
  maxRules: 10
})

const emit = defineEmits<Emits>()

// 响应式数据
const localRules = ref<IncentiveWageItem[]>([])
const ruleErrors = ref<Record<number, string>>({})
const templateModalVisible = ref(false)
const importModalVisible = ref(false)
const selectedTemplate = ref<string>('')
const importText = ref('')

// 计算属性
const hasRules = computed(() => localRules.value.length > 0)

const maxReward = computed(() => {
  if (!hasRules.value) return 0
  return Math.max(...localRules.value.map(rule => rule.reward || 0))
})

const totalReward = computed(() => {
  if (!hasRules.value) return 0
  return localRules.value.reduce((sum, rule) => sum + (rule.reward || 0), 0)
})

// 预设模板
const templates = [
  {
    id: 'basic',
    name: '基础激励',
    description: '适用于一般生产场景',
    rules: [
      { cumulative: 10000, reward: 50 },
      { cumulative: 50000, reward: 200 },
      { cumulative: 100000, reward: 500 }
    ]
  },
  {
    id: 'advanced',
    name: '高级激励',
    description: '适用于高产量要求',
    rules: [
      { cumulative: 5000, reward: 20 },
      { cumulative: 20000, reward: 100 },
      { cumulative: 50000, reward: 300 },
      { cumulative: 100000, reward: 800 },
      { cumulative: 200000, reward: 2000 }
    ]
  },
  {
    id: 'simple',
    name: '简单激励',
    description: '适用于新手或低要求场景',
    rules: [
      { cumulative: 20000, reward: 100 },
      { cumulative: 100000, reward: 600 }
    ]
  }
]

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    localRules.value = newValue ? [...newValue] : []
    clearErrors()
  },
  { immediate: true, deep: true }
)

// 监听本地规则变化
watch(
  localRules,
  (newRules) => {
    emit('update:modelValue', [...newRules])
    emit('change', [...newRules])
    validateAllRules()
  },
  { deep: true }
)

// 组件方法
const addRule = () => {
  if (localRules.value.length >= props.maxRules) {
    message.warning(`最多只能添加${props.maxRules}条规则`)
    return
  }

  const newRule: IncentiveWageItem = {
    cumulative: 0,
    reward: 0
  }

  localRules.value.push(newRule)
  message.success('添加规则成功')
}

const removeRule = (index: number) => {
  localRules.value.splice(index, 1)
  delete ruleErrors.value[index]

  // 重新索引错误信息
  const newErrors: Record<number, string> = {}
  Object.keys(ruleErrors.value).forEach(key => {
    const keyIndex = parseInt(key)
    if (keyIndex > index) {
      newErrors[keyIndex - 1] = ruleErrors.value[keyIndex]
    } else if (keyIndex < index) {
      newErrors[keyIndex] = ruleErrors.value[keyIndex]
    }
  })
  ruleErrors.value = newErrors

  message.success('删除规则成功')
}

const handleRuleChange = (index: number) => {
  // 清除当前规则的错误
  delete ruleErrors.value[index]

  // 自动排序
  sortRules()
}

const validateRule = (index: number): boolean => {
  const rule = localRules.value[index]
  if (!rule) return false

  // 验证累计针数
  if (!rule.cumulative || rule.cumulative <= 0) {
    ruleErrors.value[index] = '累计针数必须大于0'
    return false
  }

  // 验证奖励金额
  if (rule.reward === undefined || rule.reward === null || rule.reward < 0) {
    ruleErrors.value[index] = '奖励金额不能为负数'
    return false
  }

  // 验证重复的累计针数
  const duplicateIndex = localRules.value.findIndex((r, i) =>
    i !== index && r.cumulative === rule.cumulative
  )
  if (duplicateIndex !== -1) {
    ruleErrors.value[index] = '累计针数不能重复'
    return false
  }

  // 清除错误
  delete ruleErrors.value[index]
  return true
}

const validateAllRules = (): boolean => {
  let isValid = true
  const errors: string[] = []

  localRules.value.forEach((rule, index) => {
    if (!validateRule(index)) {
      isValid = false
      if (ruleErrors.value[index]) {
        errors.push(`第${index + 1}条规则：${ruleErrors.value[index]}`)
      }
    }
  })

  emit('validate', isValid, errors)
  return isValid
}

const sortRules = () => {
  localRules.value.sort((a, b) => (a.cumulative || 0) - (b.cumulative || 0))
}

const clearErrors = () => {
  ruleErrors.value = {}
}

const clearAllRules = () => {
  localRules.value = []
  clearErrors()
  message.success('已清空所有规则')
}

// 模板相关方法
const showTemplateModal = () => {
  selectedTemplate.value = ''
  templateModalVisible.value = true
}

const applyTemplate = () => {
  const template = templates.find(t => t.id === selectedTemplate.value)
  if (!template) {
    message.error('请选择一个模板')
    return
  }

  localRules.value = [...template.rules]
  templateModalVisible.value = false
  message.success(`已应用模板：${template.name}`)
}

// 导入导出方法
const showImportModal = () => {
  importText.value = ''
  importModalVisible.value = true
}

const importRules = () => {
  if (!importText.value.trim()) {
    message.error('请输入要导入的规则')
    return
  }

  try {
    const lines = importText.value.trim().split('\n')
    const newRules: IncentiveWageItem[] = []

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      if (!line) continue

      const parts = line.split(',')
      if (parts.length !== 2) {
        throw new Error(`第${i + 1}行格式错误，应为：累计针数,奖励金额`)
      }

      const cumulative = parseInt(parts[0].trim())
      const reward = parseFloat(parts[1].trim())

      if (isNaN(cumulative) || cumulative <= 0) {
        throw new Error(`第${i + 1}行累计针数无效`)
      }

      if (isNaN(reward) || reward < 0) {
        throw new Error(`第${i + 1}行奖励金额无效`)
      }

      newRules.push({ cumulative, reward })
    }

    if (newRules.length === 0) {
      throw new Error('没有有效的规则数据')
    }

    if (newRules.length > props.maxRules) {
      throw new Error(`导入的规则数量超过限制（最多${props.maxRules}条）`)
    }

    // 按累计针数排序
    newRules.sort((a, b) => a.cumulative - b.cumulative)

    // 检查重复
    for (let i = 1; i < newRules.length; i++) {
      if (newRules[i].cumulative === newRules[i - 1].cumulative) {
        throw new Error(`累计针数${newRules[i].cumulative}重复`)
      }
    }

    localRules.value = newRules
    importModalVisible.value = false
    message.success(`成功导入${newRules.length}条规则`)
  } catch (error: any) {
    message.error(error.message || '导入失败')
  }
}

const exportRules = () => {
  if (!hasRules.value) {
    message.warning('没有可导出的规则')
    return
  }

  const exportData = localRules.value
    .map(rule => `${rule.cumulative},${rule.reward}`)
    .join('\n')

  // 创建下载链接
  const blob = new Blob([exportData], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `激励工价配置_${new Date().toISOString().slice(0, 10)}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  message.success('导出成功')
}

// 暴露方法给父组件
defineExpose({
  addRule,
  removeRule,
  validateAllRules,
  sortRules,
  clearAllRules
})

// 组件选项
defineOptions({
  name: 'IncentiveWageForm'
})
</script>

<style scoped>
.incentive-wage-form {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fff;
  border-radius: 8px 8px 0 0;
}

.header-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.description {
  font-size: 12px;
  color: #8c8c8c;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.rules-container {
  padding: 16px 20px;
}

.rules-header {
  display: grid;
  grid-template-columns: 1fr 1fr 60px;
  gap: 16px;
  padding: 8px 0;
  margin-bottom: 12px;
  font-weight: 500;
  color: #595959;
  font-size: 13px;
}

.rules-list {
  max-height: 400px;
  overflow-y: auto;
}

.rule-item {
  display: grid;
  grid-template-columns: 1fr 1fr 60px;
  gap: 16px;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-item.rule-error {
  background: #fff2f0;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.col-cumulative,
.col-reward {
  display: flex;
  align-items: center;
  gap: 8px;
}

.col-actions {
  display: flex;
  justify-content: center;
}

.unit {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
}

.reward-prefix {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
}

.rule-error-message {
  grid-column: 1 / -1;
  margin-top: 8px;
  padding: 4px 8px;
  background: #fff1f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  font-size: 12px;
  color: #ff4d4f;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.rules-summary {
  padding: 12px 20px;
  border-top: 1px solid #e8e8e8;
  background: #f8f9fa;
  border-radius: 0 0 8px 8px;
}

.summary-item {
  font-size: 12px;
}

.summary-item .label {
  color: #8c8c8c;
}

.summary-item .value {
  color: #262626;
  font-weight: 500;
}

.template-list {
  max-height: 300px;
  overflow-y: auto;
}

.template-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.template-item:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

.template-item.template-selected {
  border-color: #1890ff;
  background: #e6f7ff;
}

.template-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.template-description {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.template-rules {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.template-rule {
  padding: 2px 6px;
  background: #f0f0f0;
  border-radius: 3px;
  font-size: 11px;
  color: #595959;
}

.import-content {
  padding: 8px 0;
}

.import-description {
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.5;
}

.import-description code {
  background: #fff;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Courier New', monospace;
}

.import-textarea {
  margin-bottom: 12px;
}

.import-example {
  padding: 8px 12px;
  background: #f0f2f5;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .rules-header,
  .rule-item {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .col-cumulative,
  .col-reward {
    justify-content: space-between;
  }

  .col-actions {
    justify-content: flex-end;
  }
}
</style>
