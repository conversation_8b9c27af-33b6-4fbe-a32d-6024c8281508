/**
 * WageConfig集成测试
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 18:05:00 +08:00; Reason: Task-010 编写单元测试和集成测试, 创建WageConfig集成测试; Principle_Applied: 集成测试;}}
 */

import request from 'supertest'
import { app } from '../../../app'
import { sequelize } from '../../../shared/config/database'
import { WageConfig, WageType } from '../wageConfig.model'
import { Pattern } from '../../pattern/pattern.model'
import { Enterprise } from '../../enterprise/enterprise.model'
import { User } from '../../user/user.model'
import { Role } from '../../role/role.model'
import { generateToken } from '../../../shared/utils/jwt'

describe('WageConfig Integration Tests', () => {
  let authToken: string
  let enterpriseId: number
  let userId: number
  let patternId: number

  beforeAll(async () => {
    // 同步数据库
    await sequelize.sync({ force: true })

    // 创建测试企业
    const enterprise = await Enterprise.create({
      name: '测试企业',
      code: 'TEST001',
      address: '测试地址',
      contactPerson: '测试联系人',
      contactPhone: '13800138000'
    })
    enterpriseId = enterprise.id

    // 创建测试角色
    const role = await Role.create({
      name: '测试角色',
      code: 'TEST_ROLE',
      enterpriseId,
      permissions: ['salary_rate:view', 'salary_rate:create', 'salary_rate:update', 'salary_rate:delete']
    })

    // 创建测试用户
    const user = await User.create({
      username: 'testuser',
      password: 'password123',
      name: '测试用户',
      email: '<EMAIL>',
      phone: '13800138000',
      enterpriseId,
      roleId: role.id,
      status: 1
    })
    userId = user.id

    // 创建测试花样
    const pattern = await Pattern.create({
      name: '测试花样',
      code: 'PATTERN001',
      description: '测试花样描述',
      enterpriseId,
      createdUserId: userId
    })
    patternId = pattern.id

    // 生成认证token
    authToken = generateToken({
      userId: user.id,
      enterpriseId: enterprise.id,
      roleId: role.id
    })
  })

  afterAll(async () => {
    await sequelize.close()
  })

  beforeEach(async () => {
    // 清理工价配置数据
    await WageConfig.destroy({ where: {} })
  })

  describe('GET /api/v1/wage-configs', () => {
    beforeEach(async () => {
      // 创建测试数据
      await WageConfig.bulkCreate([
        {
          type: WageType.PATTERN_WAGE,
          patternId,
          patternWage: 100.50,
          incentiveWage: [
            { cumulative: 10000, reward: 50 },
            { cumulative: 50000, reward: 200 }
          ],
          enterpriseId,
          createdUserId: userId
        },
        {
          type: WageType.CHANGE_PIECE_WAGE,
          changePieceWage: 5.00,
          enterpriseId,
          createdUserId: userId
        },
        {
          type: WageType.CHANGE_LINE_WAGE,
          changeLineWage: 3.00,
          enterpriseId,
          createdUserId: userId
        }
      ])
    })

    it('应该成功获取工价配置列表', async () => {
      const response = await request(app)
        .get('/api/v1/wage-configs')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.code).toBe(200)
      expect(response.body.data.wageConfigs).toHaveLength(3)
      expect(response.body.data.total).toBe(3)
      expect(response.body.data.page).toBe(1)
      expect(response.body.data.pageSize).toBe(10)
    })

    it('应该支持分页查询', async () => {
      const response = await request(app)
        .get('/api/v1/wage-configs?page=1&pageSize=2')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.data.wageConfigs).toHaveLength(2)
      expect(response.body.data.pageSize).toBe(2)
    })

    it('应该支持类型筛选', async () => {
      const response = await request(app)
        .get(`/api/v1/wage-configs?type=${WageType.PATTERN_WAGE}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.data.wageConfigs).toHaveLength(1)
      expect(response.body.data.wageConfigs[0].type).toBe(WageType.PATTERN_WAGE)
    })

    it('应该支持花样筛选', async () => {
      const response = await request(app)
        .get(`/api/v1/wage-configs?patternId=${patternId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.data.wageConfigs).toHaveLength(1)
      expect(response.body.data.wageConfigs[0].patternId).toBe(patternId)
    })

    it('应该处理无效的查询参数', async () => {
      const response = await request(app)
        .get('/api/v1/wage-configs?page=invalid&pageSize=0')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400)

      expect(response.body.code).toBe(400)
      expect(response.body.message).toBe('查询参数验证失败')
    })

    it('应该要求认证', async () => {
      await request(app)
        .get('/api/v1/wage-configs')
        .expect(401)
    })
  })

  describe('GET /api/v1/wage-configs/:id', () => {
    let wageConfigId: number

    beforeEach(async () => {
      const wageConfig = await WageConfig.create({
        type: WageType.PATTERN_WAGE,
        patternId,
        patternWage: 100.50,
        incentiveWage: [
          { cumulative: 10000, reward: 50 }
        ],
        enterpriseId,
        createdUserId: userId
      })
      wageConfigId = wageConfig.id
    })

    it('应该成功获取工价配置详情', async () => {
      const response = await request(app)
        .get(`/api/v1/wage-configs/${wageConfigId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.code).toBe(200)
      expect(response.body.data.id).toBe(wageConfigId)
      expect(response.body.data.type).toBe(WageType.PATTERN_WAGE)
      expect(response.body.data.patternWage).toBe(100.50)
    })

    it('应该处理不存在的工价配置', async () => {
      const response = await request(app)
        .get('/api/v1/wage-configs/999999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404)

      expect(response.body.code).toBe(404)
      expect(response.body.message).toBe('工价配置不存在')
    })

    it('应该处理无效的ID参数', async () => {
      const response = await request(app)
        .get('/api/v1/wage-configs/invalid')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400)

      expect(response.body.code).toBe(400)
      expect(response.body.message).toBe('参数验证失败')
    })
  })

  describe('POST /api/v1/wage-configs', () => {
    it('应该成功创建花样工价配置', async () => {
      const createData = {
        type: WageType.PATTERN_WAGE,
        patternId,
        patternWage: 100.50,
        incentiveWage: [
          { cumulative: 10000, reward: 50 },
          { cumulative: 50000, reward: 200 }
        ]
      }

      const response = await request(app)
        .post('/api/v1/wage-configs')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createData)
        .expect(201)

      expect(response.body.code).toBe(201)
      expect(response.body.data.type).toBe(WageType.PATTERN_WAGE)
      expect(response.body.data.patternWage).toBe(100.50)
      expect(response.body.data.incentiveWage).toHaveLength(2)
    })

    it('应该成功创建换片单价配置', async () => {
      const createData = {
        type: WageType.CHANGE_PIECE_WAGE,
        changePieceWage: 5.00
      }

      const response = await request(app)
        .post('/api/v1/wage-configs')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createData)
        .expect(201)

      expect(response.body.data.type).toBe(WageType.CHANGE_PIECE_WAGE)
      expect(response.body.data.changePieceWage).toBe(5.00)
    })

    it('应该成功创建换线单价配置', async () => {
      const createData = {
        type: WageType.CHANGE_LINE_WAGE,
        changeLineWage: 3.00
      }

      const response = await request(app)
        .post('/api/v1/wage-configs')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createData)
        .expect(201)

      expect(response.body.data.type).toBe(WageType.CHANGE_LINE_WAGE)
      expect(response.body.data.changeLineWage).toBe(3.00)
    })

    it('应该验证数据格式', async () => {
      const invalidData = {
        type: 'invalid',
        patternWage: -100
      }

      const response = await request(app)
        .post('/api/v1/wage-configs')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400)

      expect(response.body.code).toBe(400)
      expect(response.body.message).toBe('数据验证失败')
    })

    it('应该检查花样工价的重复配置', async () => {
      // 先创建一个花样工价配置
      await WageConfig.create({
        type: WageType.PATTERN_WAGE,
        patternId,
        patternWage: 100.50,
        enterpriseId,
        createdUserId: userId
      })

      const duplicateData = {
        type: WageType.PATTERN_WAGE,
        patternId,
        patternWage: 150.00
      }

      const response = await request(app)
        .post('/api/v1/wage-configs')
        .set('Authorization', `Bearer ${authToken}`)
        .send(duplicateData)
        .expect(400)

      expect(response.body.message).toContain('已存在工价配置')
    })

    it('应该检查换片单价的企业唯一性', async () => {
      // 先创建一个换片单价配置
      await WageConfig.create({
        type: WageType.CHANGE_PIECE_WAGE,
        changePieceWage: 5.00,
        enterpriseId,
        createdUserId: userId
      })

      const duplicateData = {
        type: WageType.CHANGE_PIECE_WAGE,
        changePieceWage: 6.00
      }

      const response = await request(app)
        .post('/api/v1/wage-configs')
        .set('Authorization', `Bearer ${authToken}`)
        .send(duplicateData)
        .expect(400)

      expect(response.body.message).toContain('已存在换片单价配置')
    })

    it('应该验证激励工价格式', async () => {
      const invalidIncentiveData = {
        type: WageType.PATTERN_WAGE,
        patternId,
        incentiveWage: [
          { cumulative: 50000, reward: 200 },
          { cumulative: 10000, reward: 50 } // 错误：不是递增顺序
        ]
      }

      const response = await request(app)
        .post('/api/v1/wage-configs')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidIncentiveData)
        .expect(400)

      expect(response.body.message).toBe('数据验证失败')
    })
  })

  describe('PUT /api/v1/wage-configs/:id', () => {
    let wageConfigId: number

    beforeEach(async () => {
      const wageConfig = await WageConfig.create({
        type: WageType.PATTERN_WAGE,
        patternId,
        patternWage: 100.50,
        enterpriseId,
        createdUserId: userId
      })
      wageConfigId = wageConfig.id
    })

    it('应该成功更新工价配置', async () => {
      const updateData = {
        patternWage: 150.00,
        incentiveWage: [
          { cumulative: 20000, reward: 100 }
        ]
      }

      const response = await request(app)
        .put(`/api/v1/wage-configs/${wageConfigId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200)

      expect(response.body.data.patternWage).toBe(150.00)
      expect(response.body.data.incentiveWage).toHaveLength(1)
    })

    it('应该处理不存在的工价配置', async () => {
      const response = await request(app)
        .put('/api/v1/wage-configs/999999')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ patternWage: 150.00 })
        .expect(404)

      expect(response.body.message).toBe('工价配置不存在')
    })
  })

  describe('DELETE /api/v1/wage-configs/:id', () => {
    let wageConfigId: number

    beforeEach(async () => {
      const wageConfig = await WageConfig.create({
        type: WageType.PATTERN_WAGE,
        patternId,
        patternWage: 100.50,
        enterpriseId,
        createdUserId: userId
      })
      wageConfigId = wageConfig.id
    })

    it('应该成功删除工价配置', async () => {
      const response = await request(app)
        .delete(`/api/v1/wage-configs/${wageConfigId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.message).toBe('删除工价配置成功')

      // 验证已删除
      const deletedConfig = await WageConfig.findByPk(wageConfigId)
      expect(deletedConfig).toBeNull()
    })

    it('应该处理不存在的工价配置', async () => {
      const response = await request(app)
        .delete('/api/v1/wage-configs/999999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404)

      expect(response.body.message).toBe('工价配置不存在')
    })
  })

  describe('GET /api/v1/wage-configs/search-options', () => {
    beforeEach(async () => {
      await WageConfig.create({
        type: WageType.PATTERN_WAGE,
        patternId,
        patternWage: 100.50,
        enterpriseId,
        createdUserId: userId
      })
    })

    it('应该成功获取搜索选项', async () => {
      const response = await request(app)
        .get('/api/v1/wage-configs/search-options')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.data.patterns).toHaveLength(1)
      expect(response.body.data.createdUsers).toHaveLength(1)
      expect(response.body.data.types).toHaveLength(3)
    })
  })

  describe('权限验证', () => {
    it('应该验证查看权限', async () => {
      // 创建无权限用户
      const noPermissionRole = await Role.create({
        name: '无权限角色',
        code: 'NO_PERMISSION',
        enterpriseId,
        permissions: []
      })

      const noPermissionUser = await User.create({
        username: 'nopermission',
        password: 'password123',
        name: '无权限用户',
        email: '<EMAIL>',
        phone: '13800138001',
        enterpriseId,
        roleId: noPermissionRole.id,
        status: 1
      })

      const noPermissionToken = generateToken({
        userId: noPermissionUser.id,
        enterpriseId,
        roleId: noPermissionRole.id
      })

      const response = await request(app)
        .get('/api/v1/wage-configs')
        .set('Authorization', `Bearer ${noPermissionToken}`)
        .expect(403)

      expect(response.body.message).toContain('权限不足')
    })
  })
})
