/**
 * WageConfig服务层单元测试
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 17:45:00 +08:00; Reason: Task-010 编写单元测试和集成测试, 创建WageConfig服务层测试; Principle_Applied: 单元测试;}}
 */

import { WageConfigService } from '../wageConfig.service'
import { WageConfig, WageType } from '../wageConfig.model'
import { Pattern } from '../../pattern/pattern.model'
import { createApiError } from '../../../shared/middleware/error.middleware'

// Mock dependencies
jest.mock('../wageConfig.model')
jest.mock('../../pattern/pattern.model')
jest.mock('../../../shared/middleware/error.middleware')

describe('WageConfigService', () => {
  let wageConfigService: WageConfigService
  let mockWageConfig: any
  let mockPattern: any

  beforeEach(() => {
    wageConfigService = new WageConfigService()
    
    // Reset mocks
    jest.clearAllMocks()
    
    // Mock WageConfig model
    mockWageConfig = {
      id: 1,
      enterpriseId: 1,
      type: WageType.PATTERN_WAGE,
      patternId: 1,
      patternWage: 100.50,
      incentiveWage: [
        { cumulative: 10000, reward: 50 },
        { cumulative: 50000, reward: 200 }
      ],
      createdUserId: 1,
      updatedUserId: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      update: jest.fn(),
      destroy: jest.fn()
    }

    // Mock Pattern model
    mockPattern = {
      id: 1,
      name: '测试花样',
      code: 'TEST001',
      enterpriseId: 1
    }

    // Setup default mocks
    ;(WageConfig.create as jest.Mock).mockResolvedValue(mockWageConfig)
    ;(WageConfig.findOne as jest.Mock).mockResolvedValue(mockWageConfig)
    ;(WageConfig.findAndCountAll as jest.Mock).mockResolvedValue({
      rows: [mockWageConfig],
      count: 1
    })
    ;(Pattern.findOne as jest.Mock).mockResolvedValue(mockPattern)
  })

  describe('createWageConfig', () => {
    const validCreateData = {
      type: WageType.PATTERN_WAGE,
      patternId: 1,
      patternWage: 100.50,
      incentiveWage: [
        { cumulative: 10000, reward: 50 },
        { cumulative: 50000, reward: 200 }
      ]
    }

    it('应该成功创建花样工价配置', async () => {
      const result = await wageConfigService.createWageConfig(validCreateData, 1)

      expect(WageConfig.create).toHaveBeenCalledWith({
        ...validCreateData,
        enterpriseId: 1,
        createdUserId: null,
        updatedUserId: null
      })
      expect(result).toEqual(mockWageConfig)
    })

    it('应该成功创建换片单价配置', async () => {
      const changePieceData = {
        type: WageType.CHANGE_PIECE_WAGE,
        changePieceWage: 5.00
      }

      await wageConfigService.createWageConfig(changePieceData, 1)

      expect(WageConfig.create).toHaveBeenCalledWith({
        ...changePieceData,
        enterpriseId: 1,
        createdUserId: null,
        updatedUserId: null
      })
    })

    it('应该成功创建换线单价配置', async () => {
      const changeLineData = {
        type: WageType.CHANGE_LINE_WAGE,
        changeLineWage: 3.00
      }

      await wageConfigService.createWageConfig(changeLineData, 1)

      expect(WageConfig.create).toHaveBeenCalledWith({
        ...changeLineData,
        enterpriseId: 1,
        createdUserId: null,
        updatedUserId: null
      })
    })

    it('应该验证激励工价格式', async () => {
      const invalidIncentiveData = {
        type: WageType.PATTERN_WAGE,
        patternId: 1,
        incentiveWage: [
          { cumulative: 50000, reward: 200 },
          { cumulative: 10000, reward: 50 } // 错误：不是递增顺序
        ]
      }

      await expect(
        wageConfigService.createWageConfig(invalidIncentiveData, 1)
      ).rejects.toThrow()
    })

    it('应该检查花样工价的重复配置', async () => {
      // Mock existing config
      ;(WageConfig.findOne as jest.Mock).mockResolvedValueOnce(mockWageConfig)

      await expect(
        wageConfigService.createWageConfig(validCreateData, 1)
      ).rejects.toThrow('该花样已存在工价配置')
    })

    it('应该检查换片单价的企业唯一性', async () => {
      const changePieceData = {
        type: WageType.CHANGE_PIECE_WAGE,
        changePieceWage: 5.00
      }

      // Mock existing config
      ;(WageConfig.findOne as jest.Mock).mockResolvedValueOnce(mockWageConfig)

      await expect(
        wageConfigService.createWageConfig(changePieceData, 1)
      ).rejects.toThrow('企业已存在换片单价配置')
    })

    it('应该验证花样是否存在', async () => {
      // Mock pattern not found
      ;(Pattern.findOne as jest.Mock).mockResolvedValueOnce(null)

      await expect(
        wageConfigService.createWageConfig(validCreateData, 1)
      ).rejects.toThrow('指定的花样不存在')
    })
  })

  describe('updateWageConfig', () => {
    const updateData = {
      patternWage: 150.00,
      incentiveWage: [
        { cumulative: 20000, reward: 100 }
      ]
    }

    it('应该成功更新工价配置', async () => {
      const result = await wageConfigService.updateWageConfig(1, updateData, 1)

      expect(mockWageConfig.update).toHaveBeenCalledWith({
        ...updateData,
        updatedUserId: null
      })
      expect(result).toEqual(mockWageConfig)
    })

    it('应该在工价配置不存在时抛出错误', async () => {
      ;(WageConfig.findOne as jest.Mock).mockResolvedValueOnce(null)

      await expect(
        wageConfigService.updateWageConfig(999, updateData, 1)
      ).rejects.toThrow('工价配置不存在')
    })

    it('应该验证更新后的业务规则', async () => {
      const typeChangeData = {
        type: WageType.CHANGE_PIECE_WAGE,
        changePieceWage: 5.00
      }

      // Mock existing change piece config
      ;(WageConfig.findOne as jest.Mock)
        .mockResolvedValueOnce(mockWageConfig) // 当前配置
        .mockResolvedValueOnce({ id: 2 }) // 冲突配置

      await expect(
        wageConfigService.updateWageConfig(1, typeChangeData, 1)
      ).rejects.toThrow('企业已存在换片单价配置')
    })
  })

  describe('deleteWageConfig', () => {
    it('应该成功删除工价配置', async () => {
      await wageConfigService.deleteWageConfig(1, 1)

      expect(mockWageConfig.destroy).toHaveBeenCalled()
    })

    it('应该在工价配置不存在时抛出错误', async () => {
      ;(WageConfig.findOne as jest.Mock).mockResolvedValueOnce(null)

      await expect(
        wageConfigService.deleteWageConfig(999, 1)
      ).rejects.toThrow('工价配置不存在')
    })
  })

  describe('getWageConfigById', () => {
    it('应该成功获取工价配置详情', async () => {
      const result = await wageConfigService.getWageConfigById(1, 1)

      expect(WageConfig.findOne).toHaveBeenCalledWith({
        where: { id: 1, enterpriseId: 1 },
        include: expect.any(Array)
      })
      expect(result).toEqual(mockWageConfig)
    })

    it('应该在工价配置不存在时抛出错误', async () => {
      ;(WageConfig.findOne as jest.Mock).mockResolvedValueOnce(null)

      await expect(
        wageConfigService.getWageConfigById(999, 1)
      ).rejects.toThrow('工价配置不存在')
    })
  })

  describe('getWageConfigList', () => {
    const queryParams = {
      page: 1,
      pageSize: 10,
      enterpriseId: 1
    }

    it('应该成功获取工价配置列表', async () => {
      const result = await wageConfigService.getWageConfigList(queryParams)

      expect(WageConfig.findAndCountAll).toHaveBeenCalledWith({
        where: { enterpriseId: 1 },
        include: expect.any(Array),
        limit: 10,
        offset: 0,
        order: [['createdAt', 'DESC']]
      })

      expect(result).toEqual({
        wageConfigs: [mockWageConfig],
        total: 1,
        page: 1,
        pageSize: 10,
        totalPages: 1
      })
    })

    it('应该支持搜索过滤', async () => {
      const searchParams = {
        ...queryParams,
        search: '测试',
        type: WageType.PATTERN_WAGE,
        patternId: 1
      }

      await wageConfigService.getWageConfigList(searchParams)

      expect(WageConfig.findAndCountAll).toHaveBeenCalledWith({
        where: expect.objectContaining({
          enterpriseId: 1,
          type: WageType.PATTERN_WAGE,
          patternId: 1
        }),
        include: expect.any(Array),
        limit: 10,
        offset: 0,
        order: [['createdAt', 'DESC']]
      })
    })
  })

  describe('validateIncentiveWage', () => {
    it('应该验证有效的激励工价', () => {
      const validIncentiveWage = [
        { cumulative: 10000, reward: 50 },
        { cumulative: 50000, reward: 200 }
      ]

      expect(() => {
        wageConfigService['validateIncentiveWage'](validIncentiveWage)
      }).not.toThrow()
    })

    it('应该拒绝非递增的累计针数', () => {
      const invalidIncentiveWage = [
        { cumulative: 50000, reward: 200 },
        { cumulative: 10000, reward: 50 }
      ]

      expect(() => {
        wageConfigService['validateIncentiveWage'](invalidIncentiveWage)
      }).toThrow('激励工价的累计针数必须递增')
    })

    it('应该拒绝重复的累计针数', () => {
      const invalidIncentiveWage = [
        { cumulative: 10000, reward: 50 },
        { cumulative: 10000, reward: 100 }
      ]

      expect(() => {
        wageConfigService['validateIncentiveWage'](invalidIncentiveWage)
      }).toThrow('激励工价的累计针数不能重复')
    })

    it('应该拒绝无效的数值', () => {
      const invalidIncentiveWage = [
        { cumulative: 0, reward: 50 },
        { cumulative: 10000, reward: -10 }
      ]

      expect(() => {
        wageConfigService['validateIncentiveWage'](invalidIncentiveWage)
      }).toThrow()
    })
  })
})
