/**
 * WageConfig控制器单元测试
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 17:50:00 +08:00; Reason: Task-010 编写单元测试和集成测试, 创建WageConfig控制器测试; Principle_Applied: 单元测试;}}
 */

import { Request, Response } from 'express'
import { WageConfigController } from '../wageConfig.controller'
import { WageConfigService } from '../wageConfig.service'
import { WageType } from '../wageConfig.model'

// Mock dependencies
jest.mock('../wageConfig.service')

describe('WageConfigController', () => {
  let wageConfigController: WageConfigController
  let mockWageConfigService: jest.Mocked<WageConfigService>
  let mockRequest: Partial<Request>
  let mockResponse: Partial<Response>

  beforeEach(() => {
    // Create mocked service
    mockWageConfigService = {
      getWageConfigList: jest.fn(),
      getWageConfigById: jest.fn(),
      createWageConfig: jest.fn(),
      updateWageConfig: jest.fn(),
      deleteWageConfig: jest.fn(),
      getSearchOptions: jest.fn()
    } as any

    // Create controller with mocked service
    wageConfigController = new WageConfigController()
    ;(wageConfigController as any).wageConfigService = mockWageConfigService

    // Setup mock request and response
    mockRequest = {
      user: {
        userId: 1,
        enterpriseId: 1,
        roleId: 1
      },
      query: {},
      params: {},
      body: {}
    }

    mockResponse = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis()
    }

    jest.clearAllMocks()
  })

  describe('getWageConfigList', () => {
    const mockListResult = {
      wageConfigs: [
        {
          id: 1,
          type: WageType.PATTERN_WAGE,
          patternWage: 100.50,
          enterpriseId: 1
        }
      ],
      total: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1
    }

    it('应该成功获取工价配置列表', async () => {
      mockRequest.query = {
        page: '1',
        pageSize: '10'
      }

      mockWageConfigService.getWageConfigList.mockResolvedValue(mockListResult)

      await wageConfigController.getWageConfigList(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockWageConfigService.getWageConfigList).toHaveBeenCalledWith({
        page: 1,
        pageSize: 10,
        enterpriseId: 1
      })

      expect(mockResponse.json).toHaveBeenCalledWith({
        code: 200,
        message: '获取工价配置列表成功',
        data: mockListResult
      })
    })

    it('应该处理查询参数验证错误', async () => {
      mockRequest.query = {
        page: 'invalid',
        pageSize: '0'
      }

      await wageConfigController.getWageConfigList(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockResponse.status).toHaveBeenCalledWith(400)
      expect(mockResponse.json).toHaveBeenCalledWith({
        code: 400,
        message: '查询参数验证失败',
        error: expect.any(String)
      })
    })

    it('应该支持搜索和筛选参数', async () => {
      mockRequest.query = {
        search: '测试',
        type: '1',
        patternId: '1',
        createdUserId: '1'
      }

      mockWageConfigService.getWageConfigList.mockResolvedValue(mockListResult)

      await wageConfigController.getWageConfigList(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockWageConfigService.getWageConfigList).toHaveBeenCalledWith({
        search: '测试',
        type: 1,
        patternId: 1,
        createdUserId: 1,
        page: 1,
        pageSize: 10,
        enterpriseId: 1
      })
    })
  })

  describe('getWageConfigById', () => {
    const mockWageConfig = {
      id: 1,
      type: WageType.PATTERN_WAGE,
      patternWage: 100.50,
      enterpriseId: 1
    }

    it('应该成功获取工价配置详情', async () => {
      mockRequest.params = { id: '1' }
      mockWageConfigService.getWageConfigById.mockResolvedValue(mockWageConfig)

      await wageConfigController.getWageConfigById(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockWageConfigService.getWageConfigById).toHaveBeenCalledWith(1, 1)
      expect(mockResponse.json).toHaveBeenCalledWith({
        code: 200,
        message: '获取工价配置详情成功',
        data: mockWageConfig
      })
    })

    it('应该处理无效的ID参数', async () => {
      mockRequest.params = { id: 'invalid' }

      await wageConfigController.getWageConfigById(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockResponse.status).toHaveBeenCalledWith(400)
      expect(mockResponse.json).toHaveBeenCalledWith({
        code: 400,
        message: '参数验证失败',
        error: expect.any(String)
      })
    })
  })

  describe('createWageConfig', () => {
    const validCreateData = {
      type: WageType.PATTERN_WAGE,
      patternId: 1,
      patternWage: 100.50,
      incentiveWage: [
        { cumulative: 10000, reward: 50 }
      ]
    }

    const mockCreatedConfig = {
      id: 1,
      ...validCreateData,
      enterpriseId: 1
    }

    it('应该成功创建工价配置', async () => {
      mockRequest.body = validCreateData
      mockWageConfigService.createWageConfig.mockResolvedValue(mockCreatedConfig)

      await wageConfigController.createWageConfig(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockWageConfigService.createWageConfig).toHaveBeenCalledWith(
        validCreateData,
        1
      )

      expect(mockResponse.json).toHaveBeenCalledWith({
        code: 201,
        message: '创建工价配置成功',
        data: mockCreatedConfig
      })
    })

    it('应该处理服务层抛出的业务错误', async () => {
      mockRequest.body = validCreateData
      const error = new Error('该花样已存在工价配置')
      ;(error as any).statusCode = 400

      mockWageConfigService.createWageConfig.mockRejectedValue(error)

      await expect(
        wageConfigController.createWageConfig(
          mockRequest as Request,
          mockResponse as Response
        )
      ).rejects.toThrow('该花样已存在工价配置')
    })
  })

  describe('updateWageConfig', () => {
    const updateData = {
      patternWage: 150.00,
      incentiveWage: [
        { cumulative: 20000, reward: 100 }
      ]
    }

    const mockUpdatedConfig = {
      id: 1,
      type: WageType.PATTERN_WAGE,
      ...updateData,
      enterpriseId: 1
    }

    it('应该成功更新工价配置', async () => {
      mockRequest.params = { id: '1' }
      mockRequest.body = updateData
      mockWageConfigService.updateWageConfig.mockResolvedValue(mockUpdatedConfig)

      await wageConfigController.updateWageConfig(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockWageConfigService.updateWageConfig).toHaveBeenCalledWith(
        1,
        updateData,
        1
      )

      expect(mockResponse.json).toHaveBeenCalledWith({
        code: 200,
        message: '更新工价配置成功',
        data: mockUpdatedConfig
      })
    })

    it('应该处理无效的ID参数', async () => {
      mockRequest.params = { id: 'invalid' }
      mockRequest.body = updateData

      await wageConfigController.updateWageConfig(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockResponse.status).toHaveBeenCalledWith(400)
      expect(mockResponse.json).toHaveBeenCalledWith({
        code: 400,
        message: '参数验证失败',
        error: expect.any(String)
      })
    })
  })

  describe('deleteWageConfig', () => {
    it('应该成功删除工价配置', async () => {
      mockRequest.params = { id: '1' }
      mockWageConfigService.deleteWageConfig.mockResolvedValue()

      await wageConfigController.deleteWageConfig(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockWageConfigService.deleteWageConfig).toHaveBeenCalledWith(1, 1)
      expect(mockResponse.json).toHaveBeenCalledWith({
        code: 200,
        message: '删除工价配置成功'
      })
    })

    it('应该处理无效的ID参数', async () => {
      mockRequest.params = { id: 'invalid' }

      await wageConfigController.deleteWageConfig(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockResponse.status).toHaveBeenCalledWith(400)
      expect(mockResponse.json).toHaveBeenCalledWith({
        code: 400,
        message: '参数验证失败',
        error: expect.any(String)
      })
    })
  })

  describe('getSearchOptions', () => {
    const mockSearchOptions = {
      patterns: [
        { id: 1, name: '测试花样', code: 'TEST001' }
      ],
      createdUsers: [
        { id: 1, name: '测试用户' }
      ],
      types: [
        { label: '花样工价', value: 1 },
        { label: '换片单价', value: 2 },
        { label: '换线单价', value: 3 }
      ]
    }

    it('应该成功获取搜索选项', async () => {
      mockWageConfigService.getSearchOptions.mockResolvedValue(mockSearchOptions)

      await wageConfigController.getSearchOptions(
        mockRequest as Request,
        mockResponse as Response
      )

      expect(mockWageConfigService.getSearchOptions).toHaveBeenCalledWith(1)
      expect(mockResponse.json).toHaveBeenCalledWith({
        code: 200,
        message: '获取工价配置搜索选项成功',
        data: mockSearchOptions
      })
    })
  })

  describe('错误处理', () => {
    it('应该处理服务层抛出的未知错误', async () => {
      mockRequest.query = {}
      const error = new Error('数据库连接失败')
      mockWageConfigService.getWageConfigList.mockRejectedValue(error)

      await expect(
        wageConfigController.getWageConfigList(
          mockRequest as Request,
          mockResponse as Response
        )
      ).rejects.toThrow('数据库连接失败')
    })

    it('应该记录错误日志', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      mockRequest.params = { id: '1' }
      const error = new Error('服务器内部错误')
      mockWageConfigService.getWageConfigById.mockRejectedValue(error)

      await expect(
        wageConfigController.getWageConfigById(
          mockRequest as Request,
          mockResponse as Response
        )
      ).rejects.toThrow()

      consoleSpy.mockRestore()
    })
  })
})
