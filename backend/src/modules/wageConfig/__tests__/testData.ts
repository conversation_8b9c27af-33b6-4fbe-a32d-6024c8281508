/**
 * WageConfig测试数据
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 18:10:00 +08:00; Reason: Task-010 编写单元测试和集成测试, 创建测试数据准备文件; Principle_Applied: 测试数据管理;}}
 */

import { WageType } from '../wageConfig.model'
import type { CreateWageConfigRequest, UpdateWageConfigRequest, IncentiveWageItem } from '../wageConfig.service'

// 基础测试数据
export const testEnterpriseData = {
  name: '测试企业',
  code: 'TEST_ENTERPRISE',
  address: '测试地址',
  contactPerson: '测试联系人',
  contactPhone: '13800138000'
}

export const testUserData = {
  username: 'testuser',
  password: 'password123',
  name: '测试用户',
  email: '<EMAIL>',
  phone: '13800138000',
  status: 1
}

export const testRoleData = {
  name: '测试角色',
  code: 'TEST_ROLE',
  permissions: [
    'salary_rate:view',
    'salary_rate:create',
    'salary_rate:update',
    'salary_rate:delete'
  ]
}

export const testPatternData = {
  name: '测试花样',
  code: 'PATTERN001',
  description: '测试花样描述',
  needleCount: 50000,
  colorCount: 5,
  width: 100,
  height: 80
}

// 激励工价测试数据
export const validIncentiveWage: IncentiveWageItem[] = [
  { cumulative: 10000, reward: 50 },
  { cumulative: 50000, reward: 200 },
  { cumulative: 100000, reward: 500 }
]

export const invalidIncentiveWageNotIncreasing: IncentiveWageItem[] = [
  { cumulative: 50000, reward: 200 },
  { cumulative: 10000, reward: 50 }, // 错误：不是递增顺序
  { cumulative: 100000, reward: 500 }
]

export const invalidIncentiveWageDuplicate: IncentiveWageItem[] = [
  { cumulative: 10000, reward: 50 },
  { cumulative: 10000, reward: 100 }, // 错误：重复的累计针数
  { cumulative: 50000, reward: 200 }
]

export const invalidIncentiveWageNegative: IncentiveWageItem[] = [
  { cumulative: 0, reward: 50 }, // 错误：累计针数为0
  { cumulative: 10000, reward: -10 }, // 错误：负数奖励
  { cumulative: 50000, reward: 200 }
]

// 工价配置创建数据
export const validPatternWageConfig: CreateWageConfigRequest = {
  type: WageType.PATTERN_WAGE,
  patternId: 1,
  patternWage: 100.50,
  incentiveWage: validIncentiveWage
}

export const validPatternWageConfigWithoutIncentive: CreateWageConfigRequest = {
  type: WageType.PATTERN_WAGE,
  patternId: 1,
  patternWage: 100.50
}

export const validPatternWageConfigOnlyIncentive: CreateWageConfigRequest = {
  type: WageType.PATTERN_WAGE,
  patternId: 1,
  incentiveWage: validIncentiveWage
}

export const validChangePieceWageConfig: CreateWageConfigRequest = {
  type: WageType.CHANGE_PIECE_WAGE,
  changePieceWage: 5.00
}

export const validChangeLineWageConfig: CreateWageConfigRequest = {
  type: WageType.CHANGE_LINE_WAGE,
  changeLineWage: 3.00
}

// 无效的创建数据
export const invalidWageConfigMissingType = {
  patternId: 1,
  patternWage: 100.50
}

export const invalidWageConfigInvalidType = {
  type: 999, // 无效的工价类型
  patternWage: 100.50
}

export const invalidPatternWageConfigMissingPattern: CreateWageConfigRequest = {
  type: WageType.PATTERN_WAGE,
  patternWage: 100.50
  // 缺少 patternId
}

export const invalidPatternWageConfigMissingWageAndIncentive: CreateWageConfigRequest = {
  type: WageType.PATTERN_WAGE,
  patternId: 1
  // 既没有 patternWage 也没有 incentiveWage
}

export const invalidChangePieceWageConfigMissingWage = {
  type: WageType.CHANGE_PIECE_WAGE
  // 缺少 changePieceWage
}

export const invalidChangeLineWageConfigMissingWage = {
  type: WageType.CHANGE_LINE_WAGE
  // 缺少 changeLineWage
}

export const invalidWageConfigNegativeWage: CreateWageConfigRequest = {
  type: WageType.PATTERN_WAGE,
  patternId: 1,
  patternWage: -100.50 // 负数工价
}

export const invalidWageConfigWithInvalidIncentive: CreateWageConfigRequest = {
  type: WageType.PATTERN_WAGE,
  patternId: 1,
  patternWage: 100.50,
  incentiveWage: invalidIncentiveWageNotIncreasing
}

// 更新数据
export const validUpdateData: UpdateWageConfigRequest = {
  patternWage: 150.00,
  incentiveWage: [
    { cumulative: 20000, reward: 100 },
    { cumulative: 80000, reward: 400 }
  ]
}

export const validUpdateTypeChange: UpdateWageConfigRequest = {
  type: WageType.CHANGE_PIECE_WAGE,
  changePieceWage: 6.00
}

// 查询参数测试数据
export const validQueryParams = {
  page: 1,
  pageSize: 10,
  search: '测试',
  type: WageType.PATTERN_WAGE,
  patternId: 1,
  createdUserId: 1
}

export const invalidQueryParams = {
  page: 0, // 无效：页码必须大于0
  pageSize: 101, // 无效：每页数量不能超过100
  type: 999 // 无效：工价类型
}

// 边界条件测试数据
export const boundaryTestData = {
  // 最大激励工价规则数量
  maxIncentiveWageRules: Array.from({ length: 20 }, (_, i) => ({
    cumulative: (i + 1) * 10000,
    reward: (i + 1) * 50
  })),

  // 超过最大规则数量
  tooManyIncentiveWageRules: Array.from({ length: 21 }, (_, i) => ({
    cumulative: (i + 1) * 10000,
    reward: (i + 1) * 50
  })),

  // 极大的累计针数
  largeIncentiveWage: [
    { cumulative: 999999999, reward: 10000 }
  ],

  // 极小的奖励金额
  smallReward: [
    { cumulative: 10000, reward: 0.01 }
  ],

  // 长搜索关键词
  longSearchKeyword: 'a'.repeat(100),

  // 超长搜索关键词
  tooLongSearchKeyword: 'a'.repeat(101)
}

// 性能测试数据
export const performanceTestData = {
  // 大量工价配置数据
  largeBatchWageConfigs: Array.from({ length: 1000 }, (_, i) => ({
    type: i % 3 === 0 ? WageType.PATTERN_WAGE : 
          i % 3 === 1 ? WageType.CHANGE_PIECE_WAGE : 
          WageType.CHANGE_LINE_WAGE,
    patternId: i % 3 === 0 ? Math.floor(i / 3) + 1 : undefined,
    patternWage: i % 3 === 0 ? 100 + i : undefined,
    changePieceWage: i % 3 === 1 ? 5 + (i * 0.1) : undefined,
    changeLineWage: i % 3 === 2 ? 3 + (i * 0.1) : undefined,
    incentiveWage: i % 3 === 0 && i % 6 === 0 ? [
      { cumulative: 10000, reward: 50 },
      { cumulative: 50000, reward: 200 }
    ] : undefined,
    enterpriseId: 1,
    createdUserId: 1
  }))
}

// 并发测试数据
export const concurrencyTestData = {
  // 并发创建相同花样工价配置（应该只有一个成功）
  concurrentPatternWageConfigs: Array.from({ length: 10 }, () => ({
    type: WageType.PATTERN_WAGE,
    patternId: 1,
    patternWage: 100.50
  })),

  // 并发创建换片单价配置（应该只有一个成功）
  concurrentChangePieceWageConfigs: Array.from({ length: 10 }, () => ({
    type: WageType.CHANGE_PIECE_WAGE,
    changePieceWage: 5.00
  }))
}

// 模拟API响应数据
export const mockApiResponses = {
  wageConfigList: {
    wageConfigs: [
      {
        id: 1,
        type: WageType.PATTERN_WAGE,
        patternId: 1,
        patternWage: 100.50,
        incentiveWage: validIncentiveWage,
        pattern: {
          id: 1,
          name: '测试花样',
          code: 'PATTERN001'
        },
        createdUser: {
          id: 1,
          name: '测试用户'
        },
        createdAt: '2025-07-27T10:00:00Z',
        updatedAt: '2025-07-27T10:00:00Z'
      },
      {
        id: 2,
        type: WageType.CHANGE_PIECE_WAGE,
        changePieceWage: 5.00,
        createdUser: {
          id: 1,
          name: '测试用户'
        },
        createdAt: '2025-07-27T11:00:00Z',
        updatedAt: '2025-07-27T11:00:00Z'
      }
    ],
    total: 2,
    page: 1,
    pageSize: 10,
    totalPages: 1
  },

  searchOptions: {
    patterns: [
      { id: 1, name: '测试花样', code: 'PATTERN001' },
      { id: 2, name: '花样2', code: 'PATTERN002' }
    ],
    createdUsers: [
      { id: 1, name: '测试用户' },
      { id: 2, name: '用户2' }
    ],
    types: [
      { label: '花样工价', value: WageType.PATTERN_WAGE },
      { label: '换片单价', value: WageType.CHANGE_PIECE_WAGE },
      { label: '换线单价', value: WageType.CHANGE_LINE_WAGE }
    ]
  }
}

// 错误响应数据
export const mockErrorResponses = {
  validationError: {
    code: 400,
    message: '数据验证失败',
    error: '工价类型必须是有效值(1-花样工价, 2-换片单价, 3-换线单价)'
  },

  notFoundError: {
    code: 404,
    message: '工价配置不存在'
  },

  conflictError: {
    code: 400,
    message: '该花样已存在工价配置，请勿重复创建'
  },

  permissionError: {
    code: 403,
    message: '权限不足，无法执行此操作'
  },

  serverError: {
    code: 500,
    message: '服务器内部错误'
  }
}

// 测试工具函数
export const testUtils = {
  /**
   * 创建测试用的激励工价数据
   * @param count 规则数量
   * @param startCumulative 起始累计针数
   * @param step 累计针数步长
   * @param baseReward 基础奖励金额
   */
  createIncentiveWageData(
    count: number, 
    startCumulative: number = 10000, 
    step: number = 10000, 
    baseReward: number = 50
  ): IncentiveWageItem[] {
    return Array.from({ length: count }, (_, i) => ({
      cumulative: startCumulative + (i * step),
      reward: baseReward + (i * 25)
    }))
  },

  /**
   * 生成随机工价配置数据
   * @param type 工价类型
   * @param enterpriseId 企业ID
   * @param patternId 花样ID（可选）
   */
  generateRandomWageConfig(
    type: WageType, 
    enterpriseId: number, 
    patternId?: number
  ): CreateWageConfigRequest {
    const baseConfig: CreateWageConfigRequest = { type }

    switch (type) {
      case WageType.PATTERN_WAGE:
        return {
          ...baseConfig,
          patternId: patternId || Math.floor(Math.random() * 100) + 1,
          patternWage: Math.round((Math.random() * 200 + 50) * 100) / 100,
          incentiveWage: Math.random() > 0.5 ? this.createIncentiveWageData(
            Math.floor(Math.random() * 5) + 1
          ) : undefined
        }

      case WageType.CHANGE_PIECE_WAGE:
        return {
          ...baseConfig,
          changePieceWage: Math.round((Math.random() * 10 + 1) * 100) / 100
        }

      case WageType.CHANGE_LINE_WAGE:
        return {
          ...baseConfig,
          changeLineWage: Math.round((Math.random() * 5 + 1) * 100) / 100
        }

      default:
        throw new Error('无效的工价类型')
    }
  }
}
