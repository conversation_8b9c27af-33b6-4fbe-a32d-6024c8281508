/**
 * WageConfig性能测试
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 18:15:00 +08:00; Reason: Task-010 编写单元测试和集成测试, 创建WageConfig性能测试; Principle_Applied: 性能测试;}}
 */

import { WageConfigService } from '../wageConfig.service'
import { WageConfig, WageType } from '../wageConfig.model'
import { Pattern } from '../../pattern/pattern.model'
import { Enterprise } from '../../enterprise/enterprise.model'
import { User } from '../../user/user.model'
import { sequelize } from '../../../shared/config/database'
import { performanceTestData, testUtils } from './testData'

describe('WageConfig Performance Tests', () => {
  let wageConfigService: WageConfigService
  let enterpriseId: number
  let userId: number
  let patternIds: number[]

  beforeAll(async () => {
    wageConfigService = new WageConfigService()
    
    // 创建测试企业
    const enterprise = await Enterprise.create({
      name: '性能测试企业',
      code: 'PERF_TEST',
      address: '测试地址',
      contactPerson: '测试联系人',
      contactPhone: '13800138000'
    })
    enterpriseId = enterprise.id

    // 创建测试用户
    const user = await User.create({
      username: 'perfuser',
      password: 'password123',
      name: '性能测试用户',
      email: '<EMAIL>',
      phone: '13800138000',
      enterpriseId,
      status: 1
    })
    userId = user.id

    // 创建大量测试花样
    const patterns = await Pattern.bulkCreate(
      Array.from({ length: 500 }, (_, i) => ({
        name: `性能测试花样${i + 1}`,
        code: `PERF${String(i + 1).padStart(3, '0')}`,
        description: `性能测试花样${i + 1}描述`,
        enterpriseId,
        createdUserId: userId
      }))
    )
    patternIds = patterns.map(p => p.id)
  })

  afterAll(async () => {
    // 清理测试数据
    await WageConfig.destroy({ where: { enterpriseId } })
    await Pattern.destroy({ where: { enterpriseId } })
    await User.destroy({ where: { enterpriseId } })
    await Enterprise.destroy({ where: { id: enterpriseId } })
  })

  beforeEach(async () => {
    // 清理工价配置数据
    await WageConfig.destroy({ where: { enterpriseId } })
  })

  describe('大数据量查询性能', () => {
    beforeEach(async () => {
      // 创建大量测试数据
      const wageConfigs = Array.from({ length: 1000 }, (_, i) => {
        const type = i % 3 === 0 ? WageType.PATTERN_WAGE : 
                    i % 3 === 1 ? WageType.CHANGE_PIECE_WAGE : 
                    WageType.CHANGE_LINE_WAGE

        return {
          type,
          patternId: type === WageType.PATTERN_WAGE ? patternIds[i % patternIds.length] : undefined,
          patternWage: type === WageType.PATTERN_WAGE ? 100 + (i * 0.1) : undefined,
          changePieceWage: type === WageType.CHANGE_PIECE_WAGE ? 5 + (i * 0.01) : undefined,
          changeLineWage: type === WageType.CHANGE_LINE_WAGE ? 3 + (i * 0.01) : undefined,
          incentiveWage: type === WageType.PATTERN_WAGE && i % 10 === 0 ? [
            { cumulative: 10000, reward: 50 },
            { cumulative: 50000, reward: 200 }
          ] : undefined,
          enterpriseId,
          createdUserId: userId
        }
      })

      await WageConfig.bulkCreate(wageConfigs)
    })

    it('应该在合理时间内完成列表查询', async () => {
      const startTime = Date.now()

      const result = await wageConfigService.getWageConfigList({
        page: 1,
        pageSize: 50,
        enterpriseId
      })

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(result.wageConfigs).toHaveLength(50)
      expect(result.total).toBe(1000)
      expect(duration).toBeLessThan(1000) // 应该在1秒内完成
    })

    it('应该在合理时间内完成搜索查询', async () => {
      const startTime = Date.now()

      const result = await wageConfigService.getWageConfigList({
        page: 1,
        pageSize: 20,
        search: '性能测试',
        type: WageType.PATTERN_WAGE,
        enterpriseId
      })

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(result.wageConfigs.length).toBeGreaterThan(0)
      expect(duration).toBeLessThan(1500) // 搜索查询应该在1.5秒内完成
    })

    it('应该在合理时间内完成分页查询', async () => {
      const startTime = Date.now()

      // 查询多页数据
      const promises = Array.from({ length: 10 }, (_, i) =>
        wageConfigService.getWageConfigList({
          page: i + 1,
          pageSize: 20,
          enterpriseId
        })
      )

      const results = await Promise.all(promises)
      const endTime = Date.now()
      const duration = endTime - startTime

      expect(results).toHaveLength(10)
      expect(results.every(r => r.wageConfigs.length <= 20)).toBe(true)
      expect(duration).toBeLessThan(3000) // 10次查询应该在3秒内完成
    })
  })

  describe('批量操作性能', () => {
    it('应该在合理时间内完成批量创建', async () => {
      const startTime = Date.now()

      // 批量创建100个工价配置
      const createPromises = Array.from({ length: 100 }, (_, i) => {
        const config = testUtils.generateRandomWageConfig(
          WageType.PATTERN_WAGE,
          enterpriseId,
          patternIds[i % patternIds.length]
        )
        return wageConfigService.createWageConfig(config, enterpriseId)
      })

      const results = await Promise.all(createPromises)
      const endTime = Date.now()
      const duration = endTime - startTime

      expect(results).toHaveLength(100)
      expect(results.every(r => r.id)).toBe(true)
      expect(duration).toBeLessThan(10000) // 批量创建应该在10秒内完成
    })

    it('应该在合理时间内完成批量更新', async () => {
      // 先创建测试数据
      const configs = await WageConfig.bulkCreate(
        Array.from({ length: 50 }, (_, i) => ({
          type: WageType.PATTERN_WAGE,
          patternId: patternIds[i],
          patternWage: 100,
          enterpriseId,
          createdUserId: userId
        }))
      )

      const startTime = Date.now()

      // 批量更新
      const updatePromises = configs.map(config =>
        wageConfigService.updateWageConfig(config.id, {
          patternWage: 150
        }, enterpriseId)
      )

      const results = await Promise.all(updatePromises)
      const endTime = Date.now()
      const duration = endTime - startTime

      expect(results).toHaveLength(50)
      expect(results.every(r => r.patternWage === 150)).toBe(true)
      expect(duration).toBeLessThan(8000) // 批量更新应该在8秒内完成
    })

    it('应该在合理时间内完成批量删除', async () => {
      // 先创建测试数据
      const configs = await WageConfig.bulkCreate(
        Array.from({ length: 30 }, (_, i) => ({
          type: WageType.PATTERN_WAGE,
          patternId: patternIds[i],
          patternWage: 100,
          enterpriseId,
          createdUserId: userId
        }))
      )

      const startTime = Date.now()

      // 批量删除
      const deletePromises = configs.map(config =>
        wageConfigService.deleteWageConfig(config.id, enterpriseId)
      )

      await Promise.all(deletePromises)
      const endTime = Date.now()
      const duration = endTime - startTime

      // 验证删除成功
      const remainingConfigs = await WageConfig.findAll({
        where: { enterpriseId }
      })

      expect(remainingConfigs).toHaveLength(0)
      expect(duration).toBeLessThan(5000) // 批量删除应该在5秒内完成
    })
  })

  describe('复杂查询性能', () => {
    beforeEach(async () => {
      // 创建复杂的测试数据
      const complexConfigs = Array.from({ length: 500 }, (_, i) => ({
        type: WageType.PATTERN_WAGE,
        patternId: patternIds[i],
        patternWage: 100 + (i * 0.5),
        incentiveWage: testUtils.createIncentiveWageData(
          Math.floor(Math.random() * 10) + 1, // 1-10条激励规则
          10000,
          5000,
          25
        ),
        enterpriseId,
        createdUserId: userId
      }))

      await WageConfig.bulkCreate(complexConfigs)
    })

    it('应该在合理时间内完成复杂条件查询', async () => {
      const startTime = Date.now()

      const result = await wageConfigService.getWageConfigList({
        page: 1,
        pageSize: 20,
        search: '性能测试',
        type: WageType.PATTERN_WAGE,
        patternId: patternIds[0],
        enterpriseId
      })

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(result.wageConfigs.length).toBeGreaterThanOrEqual(0)
      expect(duration).toBeLessThan(2000) // 复杂查询应该在2秒内完成
    })

    it('应该在合理时间内处理大量激励工价数据', async () => {
      const startTime = Date.now()

      // 查询包含激励工价的配置
      const result = await wageConfigService.getWageConfigList({
        page: 1,
        pageSize: 100,
        type: WageType.PATTERN_WAGE,
        enterpriseId
      })

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(result.wageConfigs.length).toBeGreaterThan(0)
      expect(result.wageConfigs.every(config => 
        config.incentiveWage && config.incentiveWage.length > 0
      )).toBe(true)
      expect(duration).toBeLessThan(2500) // 包含JSON数据的查询应该在2.5秒内完成
    })
  })

  describe('并发操作性能', () => {
    it('应该正确处理并发创建操作', async () => {
      const startTime = Date.now()

      // 模拟10个用户同时创建不同的工价配置
      const concurrentPromises = Array.from({ length: 10 }, (_, i) => {
        return Promise.all([
          wageConfigService.createWageConfig({
            type: WageType.PATTERN_WAGE,
            patternId: patternIds[i],
            patternWage: 100 + i
          }, enterpriseId),
          wageConfigService.createWageConfig({
            type: WageType.PATTERN_WAGE,
            patternId: patternIds[i + 10],
            patternWage: 200 + i
          }, enterpriseId)
        ])
      })

      const results = await Promise.all(concurrentPromises)
      const endTime = Date.now()
      const duration = endTime - startTime

      expect(results).toHaveLength(10)
      expect(results.flat()).toHaveLength(20)
      expect(duration).toBeLessThan(8000) // 并发创建应该在8秒内完成
    })

    it('应该正确处理并发查询操作', async () => {
      // 先创建一些数据
      await WageConfig.bulkCreate(
        Array.from({ length: 100 }, (_, i) => ({
          type: WageType.PATTERN_WAGE,
          patternId: patternIds[i],
          patternWage: 100 + i,
          enterpriseId,
          createdUserId: userId
        }))
      )

      const startTime = Date.now()

      // 模拟20个并发查询
      const concurrentQueries = Array.from({ length: 20 }, (_, i) =>
        wageConfigService.getWageConfigList({
          page: (i % 5) + 1,
          pageSize: 10,
          enterpriseId
        })
      )

      const results = await Promise.all(concurrentQueries)
      const endTime = Date.now()
      const duration = endTime - startTime

      expect(results).toHaveLength(20)
      expect(results.every(r => r.wageConfigs.length <= 10)).toBe(true)
      expect(duration).toBeLessThan(5000) // 并发查询应该在5秒内完成
    })
  })

  describe('内存使用性能', () => {
    it('应该在处理大量数据时保持合理的内存使用', async () => {
      const initialMemory = process.memoryUsage()

      // 创建大量数据
      const largeDataset = Array.from({ length: 2000 }, (_, i) => ({
        type: WageType.PATTERN_WAGE,
        patternId: patternIds[i % patternIds.length],
        patternWage: 100 + (i * 0.1),
        incentiveWage: testUtils.createIncentiveWageData(5),
        enterpriseId,
        createdUserId: userId
      }))

      await WageConfig.bulkCreate(largeDataset)

      // 执行多次查询
      for (let i = 0; i < 10; i++) {
        await wageConfigService.getWageConfigList({
          page: i + 1,
          pageSize: 100,
          enterpriseId
        })
      }

      const finalMemory = process.memoryUsage()
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed

      // 内存增长应该在合理范围内（小于100MB）
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024)
    })
  })

  describe('数据库连接性能', () => {
    it('应该有效管理数据库连接', async () => {
      const startTime = Date.now()

      // 执行大量数据库操作
      const operations = Array.from({ length: 100 }, async (_, i) => {
        if (i % 4 === 0) {
          return wageConfigService.createWageConfig({
            type: WageType.PATTERN_WAGE,
            patternId: patternIds[i % patternIds.length],
            patternWage: 100 + i
          }, enterpriseId)
        } else if (i % 4 === 1) {
          return wageConfigService.getWageConfigList({
            page: 1,
            pageSize: 5,
            enterpriseId
          })
        } else if (i % 4 === 2) {
          const configs = await WageConfig.findAll({
            where: { enterpriseId },
            limit: 1
          })
          if (configs.length > 0) {
            return wageConfigService.updateWageConfig(configs[0].id, {
              patternWage: 200
            }, enterpriseId)
          }
        } else {
          const configs = await WageConfig.findAll({
            where: { enterpriseId },
            limit: 1
          })
          if (configs.length > 0) {
            return wageConfigService.deleteWageConfig(configs[0].id, enterpriseId)
          }
        }
      })

      await Promise.all(operations)
      const endTime = Date.now()
      const duration = endTime - startTime

      // 100个混合操作应该在15秒内完成
      expect(duration).toBeLessThan(15000)
    })
  })
})
