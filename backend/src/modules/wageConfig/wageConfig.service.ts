/**
 * 工价配置服务类
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 16:05:00 +08:00; Reason: Task-003 创建WageConfig服务层, 实现CRUD操作和业务逻辑; Principle_Applied: 服务层设计模式;}}
 */

import { Op, WhereOptions } from 'sequelize';
import { WageConfig, WageConfigAttributes, WageConfigCreationAttributes, WageType, IncentiveWageItem } from '../../shared/database/models/WageConfig';
import { Pattern } from '../../shared/database/models/Pattern';
import { User } from '../../shared/database/models/User';
import { Enterprise } from '../../shared/database/models/Enterprise';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';
import {
  createWageConfigSchema,
  updateWageConfigSchema,
  WageConfigBusinessValidator
} from './wageConfig.validation';

// 工价配置列表查询参数
export interface WageConfigListQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  type?: WageType;
  patternId?: number;
  createdUserId?: number;
  enterpriseId: number;
}

// 工价配置列表响应
export interface WageConfigListResponse {
  wageConfigs: WageConfig[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 工价配置搜索选项
export interface WageConfigSearchOptions {
  patterns: Array<{ id: number; name: string; code?: string }>;
  createdUsers: Array<{ id: number; name: string }>;
  types: Array<{ label: string; value: WageType }>;
}

// 创建工价配置请求
export interface CreateWageConfigRequest {
  type: WageType;
  patternId?: number;
  patternWage?: number;
  incentiveWage?: IncentiveWageItem[];
  changePieceWage?: number;
  changeLineWage?: number;
  createdUserId?: number;
}

// 更新工价配置请求
export interface UpdateWageConfigRequest extends Partial<CreateWageConfigRequest> {
  updatedUserId?: number;
}

/**
 * 工价配置服务类
 */
export class WageConfigService {
  /**
   * 获取工价配置列表
   */
  async getWageConfigList(query: WageConfigListQuery): Promise<WageConfigListResponse> {
    try {
      const {
        page = 1,
        pageSize = 10,
        search,
        type,
        patternId,
        createdUserId,
        enterpriseId
      } = query;

      const offset = (page - 1) * pageSize;
      const limit = pageSize;

      // 构建查询条件
      const whereConditions: WhereOptions = {
        enterpriseId
      };

      // 搜索条件
      if (search) {
        whereConditions[Op.or] = [
          { '$pattern.name$': { [Op.iLike]: `%${search}%` } },
          { '$pattern.code$': { [Op.iLike]: `%${search}%` } },
          { '$createdUser.name$': { [Op.iLike]: `%${search}%` } }
        ];
      }

      // 筛选条件
      if (type) {
        whereConditions.type = type;
      }
      if (patternId) {
        whereConditions.patternId = patternId;
      }
      if (createdUserId) {
        whereConditions.createdUserId = createdUserId;
      }

      const { count, rows } = await WageConfig.findAndCountAll({
        where: whereConditions,
        offset,
        limit,
        order: [['updatedAt', 'DESC']],
        include: [
          {
            association: 'pattern',
            attributes: ['id', 'name', 'code'],
            required: false,
          },
          {
            association: 'createdUser',
            attributes: ['id', 'name'],
            required: false,
          },
          {
            association: 'updatedUser',
            attributes: ['id', 'name'],
            required: false,
          },
          {
            association: 'enterprise',
            attributes: ['id', 'name'],
            required: false,
          }
        ]
      });

      const totalPages = Math.ceil(count / pageSize);

      logger.info('获取工价配置列表成功', {
        enterpriseId,
        total: count,
        page,
        pageSize
      });

      return {
        wageConfigs: rows,
        total: count,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      logger.error('获取工价配置列表失败', {
        query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据ID获取工价配置详情
   */
  async getWageConfigById(id: number, enterpriseId: number): Promise<WageConfig> {
    try {
      const wageConfig = await WageConfig.findOne({
        where: { id, enterpriseId },
        include: [
          {
            association: 'pattern',
            attributes: ['id', 'name', 'code'],
            required: false,
          },
          {
            association: 'createdUser',
            attributes: ['id', 'name'],
            required: false,
          },
          {
            association: 'updatedUser',
            attributes: ['id', 'name'],
            required: false,
          },
          {
            association: 'enterprise',
            attributes: ['id', 'name'],
            required: false,
          }
        ]
      });

      if (!wageConfig) {
        throw createApiError(404, '工价配置不存在');
      }

      logger.info('获取工价配置详情成功', { id, enterpriseId });
      return wageConfig;
    } catch (error) {
      logger.error('获取工价配置详情失败', {
        id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建工价配置
   */
  async createWageConfig(data: CreateWageConfigRequest, enterpriseId: number): Promise<WageConfig> {
    try {
      // 1. 数据格式验证
      const { error, value } = createWageConfigSchema.validate(data, {
        abortEarly: false,
        stripUnknown: true
      });

      if (error) {
        const errorMessage = error.details.map(detail => detail.message).join('; ');
        logger.warn('工价配置数据验证失败', {
          error: errorMessage,
          data,
          enterpriseId
        });
        throw createApiError(400, '数据验证失败', errorMessage);
      }

      const validatedData = value as CreateWageConfigRequest;

      // 2. 业务规则验证
      await this.validateBusinessRules(validatedData, enterpriseId);

      // 3. 创建工价配置
      const wageConfig = await WageConfig.create({
        ...validatedData,
        enterpriseId,
        createdUserId: validatedData.createdUserId || null,
        updatedUserId: validatedData.createdUserId || null
      });

      logger.info('工价配置创建成功', {
        wageConfigId: wageConfig.id,
        enterpriseId,
        type: validatedData.type,
        createdUserId: validatedData.createdUserId
      });

      return await this.getWageConfigById(wageConfig.id, enterpriseId);
    } catch (error: any) {
      logger.error('创建工价配置失败', {
        error: error.message || '未知错误',
        data,
        enterpriseId
      });

      // 如果是已知的业务错误，直接抛出
      if (error.statusCode) {
        throw error;
      }

      throw createApiError(500, '创建工价配置失败', error.message || '未知错误');
    }
  }

  /**
   * 更新工价配置
   */
  async updateWageConfig(id: number, data: UpdateWageConfigRequest, enterpriseId: number): Promise<WageConfig> {
    try {
      // 1. 检查工价配置是否存在
      const wageConfig = await WageConfig.findOne({
        where: { id, enterpriseId }
      });

      if (!wageConfig) {
        throw createApiError(404, '工价配置不存在');
      }

      // 2. 数据格式验证
      const { error, value } = updateWageConfigSchema.validate(data, {
        abortEarly: false,
        stripUnknown: true
      });

      if (error) {
        const errorMessage = error.details.map(detail => detail.message).join('; ');
        logger.warn('工价配置更新数据验证失败', {
          error: errorMessage,
          id,
          data,
          enterpriseId
        });
        throw createApiError(400, '数据验证失败', errorMessage);
      }

      const validatedData = value as UpdateWageConfigRequest;

      // 3. 业务规则验证（如果关键字段发生变化）
      if (validatedData.type || validatedData.patternId !== undefined) {
        const mergedData = {
          type: validatedData.type || wageConfig.type,
          patternId: validatedData.patternId !== undefined ? validatedData.patternId : wageConfig.patternId,
          ...validatedData
        };
        await this.validateBusinessRules(mergedData, enterpriseId, id);
      }

      // 4. 更新工价配置
      await wageConfig.update({
        ...validatedData,
        updatedUserId: validatedData.createdUserId || null
      });

      logger.info('工价配置更新成功', {
        wageConfigId: id,
        enterpriseId,
        type: wageConfig.type,
        updatedUserId: validatedData.createdUserId
      });

      return await this.getWageConfigById(id, enterpriseId);
    } catch (error: any) {
      logger.error('更新工价配置失败', {
        id,
        data,
        enterpriseId,
        error: error.message || '未知错误'
      });

      // 如果是已知的业务错误，直接抛出
      if (error.statusCode) {
        throw error;
      }

      throw createApiError(500, '更新工价配置失败', error.message || '未知错误');
    }
  }

  /**
   * 删除工价配置
   */
  async deleteWageConfig(id: number, enterpriseId: number): Promise<void> {
    try {
      // 1. 检查工价配置是否存在
      const wageConfig = await WageConfig.findOne({
        where: { id, enterpriseId }
      });

      if (!wageConfig) {
        throw createApiError(404, '工价配置不存在');
      }

      // 2. 业务规则验证（检查是否可以删除）
      await WageConfigBusinessValidator.validateCanDelete(id, enterpriseId);

      // 3. 删除工价配置
      await wageConfig.destroy();

      logger.info('工价配置删除成功', {
        wageConfigId: id,
        enterpriseId,
        type: wageConfig.type
      });
    } catch (error: any) {
      logger.error('删除工价配置失败', {
        id,
        enterpriseId,
        error: error.message || '未知错误'
      });

      // 如果是已知的业务错误，直接抛出
      if (error.statusCode) {
        throw error;
      }

      throw createApiError(500, '删除工价配置失败', error.message || '未知错误');
    }
  }
  /**
   * 获取工价配置搜索选项
   */
  async getSearchOptions(enterpriseId: number): Promise<WageConfigSearchOptions> {
    try {
      // 查询花样选项
      const patterns = await Pattern.findAll({
        where: { enterpriseId },
        attributes: ['id', 'name', 'code'],
        order: [['name', 'ASC']]
      });

      // 查询创建人选项
      const createdUserResults = await WageConfig.findAll({
        where: {
          enterpriseId,
          createdUserId: { [Op.ne]: null }
        },
        attributes: [],
        include: [
          {
            association: 'createdUser',
            attributes: ['id', 'name'],
            required: true,
          }
        ],
        group: ['createdUser.id', 'createdUser.name'],
        order: [['createdUser', 'name', 'ASC']]
      });

      const createdUsers = createdUserResults.map(item => ({
        id: item.createdUser!.id,
        name: item.createdUser!.name
      }));

      // 工价类型选项
      const types = [
        { label: '花样工价', value: WageType.PATTERN_WAGE },
        { label: '换片单价', value: WageType.CHANGE_PIECE_WAGE },
        { label: '换线单价', value: WageType.CHANGE_LINE_WAGE }
      ];

      return {
        patterns: patterns.map(item => ({
          id: item.id,
          name: item.name,
          code: item.code
        })),
        createdUsers,
        types
      };
    } catch (error) {
      logger.error('获取工价配置搜索选项失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 验证激励工价JSON格式和业务规则
   */
  private validateIncentiveWage(incentiveWage: IncentiveWageItem[]): void {
    if (!Array.isArray(incentiveWage)) {
      throw createApiError(400, '激励工价必须是数组格式');
    }

    if (incentiveWage.length === 0) {
      return; // 空数组是允许的
    }

    // 验证每个项目的格式
    for (let i = 0; i < incentiveWage.length; i++) {
      const item = incentiveWage[i];

      if (!item.cumulative || !item.reward) {
        throw createApiError(400, `激励工价第${i + 1}项必须包含cumulative和reward字段`);
      }

      if (typeof item.cumulative !== 'number' || typeof item.reward !== 'number') {
        throw createApiError(400, `激励工价第${i + 1}项的cumulative和reward必须是数字`);
      }

      if (item.cumulative <= 0 || item.reward <= 0) {
        throw createApiError(400, `激励工价第${i + 1}项的cumulative和reward必须大于0`);
      }

      // 检查累计针数是否递增
      if (i > 0 && item.cumulative <= incentiveWage[i - 1].cumulative) {
        throw createApiError(400, `激励工价第${i + 1}项的累计针数必须大于前一项`);
      }
    }
  }
  /**
   * 验证业务规则
   */
  private async validateBusinessRules(
    data: CreateWageConfigRequest | (UpdateWageConfigRequest & { type: WageType }),
    enterpriseId: number,
    excludeId?: number
  ): Promise<void> {
    try {
      // 验证花样是否存在且属于当前企业
      if (data.patternId) {
        await WageConfigBusinessValidator.validatePatternExists(data.patternId, enterpriseId);
      }

      // 根据工价类型进行唯一性验证
      switch (data.type) {
        case WageType.PATTERN_WAGE:
          if (data.patternId) {
            await WageConfigBusinessValidator.validatePatternWageUniqueness(
              enterpriseId,
              data.patternId,
              excludeId
            );
          }
          break;

        case WageType.CHANGE_PIECE_WAGE:
          await WageConfigBusinessValidator.validateChangePieceWageUniqueness(
            enterpriseId,
            excludeId
          );
          break;

        case WageType.CHANGE_LINE_WAGE:
          await WageConfigBusinessValidator.validateChangeLineWageUniqueness(
            enterpriseId,
            excludeId
          );
          break;

        default:
          throw createApiError(400, '无效的工价类型');
      }
    } catch (error: any) {
      // 如果是业务验证错误，包装为API错误
      if (!error.statusCode) {
        throw createApiError(400, error.message);
      }
      throw error;
    }
  }
}
