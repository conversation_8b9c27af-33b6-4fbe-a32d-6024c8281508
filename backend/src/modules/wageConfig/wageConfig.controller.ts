/**
 * 工价配置控制器类
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 16:15:00 +08:00; Reason: Task-004 创建WageConfig控制器, 提供RESTful API接口; Principle_Applied: 控制器设计模式;}}
 */

import { Request, Response } from 'express';
import { WageConfigService, WageConfigListQuery } from './wageConfig.service';
import { logger } from '../../shared/utils/logger';

/**
 * 工价配置控制器类
 */
export class WageConfigController {
  private wageConfigService: WageConfigService;

  constructor() {
    this.wageConfigService = new WageConfigService();
  }

  /**
   * 获取工价配置列表
   */
  async getWageConfigList(req: Request, res: Response): Promise<void> {
    try {
      const query: WageConfigListQuery = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 10,
        search: req.query.search as string,
        type: req.query.type ? parseInt(req.query.type as string) : undefined,
        patternId: req.query.patternId ? parseInt(req.query.patternId as string) : undefined,
        createdUserId: req.query.createdUserId ? parseInt(req.query.createdUserId as string) : undefined,
        enterpriseId: req.user!.enterpriseId
      };

      const result = await this.wageConfigService.getWageConfigList(query);

      res.json({
        code: 200,
        message: '获取工价配置列表成功',
        data: result
      });
    } catch (error) {
      logger.error('获取工价配置列表失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        query: req.query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取工价配置详情
   */
  async getWageConfigById(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '无效的工价配置ID'
        });
      }

      const wageConfig = await this.wageConfigService.getWageConfigById(id, enterpriseId);

      res.json({
        code: 200,
        message: '获取工价配置详情成功',
        data: wageConfig
      });
    } catch (error) {
      logger.error('获取工价配置详情失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        wageConfigId: req.params.id,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建工价配置
   */
  async createWageConfig(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;
      const userId = req.user!.userId;
      const data = {
        ...req.body,
        createdUserId: userId
      };

      // 基本数据验证
      if (!data.type) {
        return res.status(400).json({
          code: 400,
          message: '工价类型不能为空'
        });
      }

      const wageConfig = await this.wageConfigService.createWageConfig(data, enterpriseId);

      res.status(201).json({
        code: 201,
        message: '创建工价配置成功',
        data: wageConfig
      });
    } catch (error) {
      logger.error('创建工价配置失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        data: req.body,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新工价配置
   */
  async updateWageConfig(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;
      const userId = req.user!.userId;
      const data = {
        ...req.body,
        updatedUserId: userId
      };

      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '无效的工价配置ID'
        });
      }

      const wageConfig = await this.wageConfigService.updateWageConfig(id, data, enterpriseId);

      res.json({
        code: 200,
        message: '更新工价配置成功',
        data: wageConfig
      });
    } catch (error) {
      logger.error('更新工价配置失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        wageConfigId: req.params.id,
        data: req.body,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除工价配置
   */
  async deleteWageConfig(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '无效的工价配置ID'
        });
      }

      await this.wageConfigService.deleteWageConfig(id, enterpriseId);

      res.json({
        code: 200,
        message: '删除工价配置成功'
      });
    } catch (error) {
      logger.error('删除工价配置失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        wageConfigId: req.params.id,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取工价配置搜索选项
   */
  async getSearchOptions(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;

      const options = await this.wageConfigService.getSearchOptions(enterpriseId);

      res.json({
        code: 200,
        message: '获取工价配置搜索选项成功',
        data: options
      });
    } catch (error) {
      logger.error('获取工价配置搜索选项失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}

export const wageConfigController = new WageConfigController();
