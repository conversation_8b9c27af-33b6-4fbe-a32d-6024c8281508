/**
 * 工价配置验证规则
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 17:15:00 +08:00; Reason: Task-009 添加数据验证和错误处理, 完善后端数据验证规则; Principle_Applied: 数据验证;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-27 18:30:00 +08:00; Reason: 修复验证库问题, 改用express-validator替代Joi; Principle_Applied: 项目一致性;}}
 */

import { body, param, query } from 'express-validator'
import { WageType } from '../../shared/database/models/WageConfig'

/**
 * 工价配置验证规则
 */
export const wageConfigValidationRules = {
  /**
   * 获取工价配置列表验证规则
   */
  getWageConfigList: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是大于0的整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须是1-100之间的整数'),
    query('search')
      .optional()
      .isLength({ max: 100 })
      .withMessage('搜索关键词长度不能超过100个字符'),
    query('type')
      .optional()
      .isInt()
      .custom((value) => {
        const validTypes = Object.values(WageType).filter(v => typeof v === 'number')
        if (!validTypes.includes(value)) {
          throw new Error('工价类型必须是有效值(1-花样工价, 2-换片单价, 3-换线单价)')
        }
        return true
      }),
    query('patternId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('花样ID必须是大于0的整数'),
    query('createdUserId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('创建人ID必须是大于0的整数')
  ],

  /**
   * 获取工价配置详情验证规则
   */
  getWageConfigById: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('工价配置ID必须是大于0的整数')
  ],

  /**
   * 创建工价配置验证规则
   */
  createWageConfig: [
    body('type')
      .isInt()
      .custom((value) => {
        const validTypes = Object.values(WageType).filter(v => typeof v === 'number')
        if (!validTypes.includes(value)) {
          throw new Error('工价类型必须是有效值(1-花样工价, 2-换片单价, 3-换线单价)')
        }
        return true
      }),
    body('patternId')
      .if(body('type').equals(WageType.PATTERN_WAGE))
      .isInt({ min: 1 })
      .withMessage('花样工价类型必须选择花样'),
    body('patternWage')
      .if(body('type').equals(WageType.PATTERN_WAGE))
      .optional()
      .isFloat({ min: 0 })
      .withMessage('花样工价不能为负数'),
    body('incentiveWage')
      .if(body('type').equals(WageType.PATTERN_WAGE))
      .optional()
      .isArray({ max: 20 })
      .withMessage('激励工价规则最多不能超过20条')
      .custom((value) => {
        if (!Array.isArray(value) || value.length === 0) {
          return true
        }

        // 验证每个激励工价项目
        for (let i = 0; i < value.length; i++) {
          const item = value[i]
          if (!item.cumulative || !Number.isInteger(item.cumulative) || item.cumulative <= 0) {
            throw new Error(`第${i + 1}条规则的累计针数必须是大于0的整数`)
          }
          if (item.reward === undefined || item.reward < 0) {
            throw new Error(`第${i + 1}条规则的奖励金额不能为负数`)
          }
        }

        // 检查累计针数是否递增
        for (let i = 1; i < value.length; i++) {
          if (value[i].cumulative <= value[i - 1].cumulative) {
            throw new Error(`第${i + 1}条规则的累计针数必须大于前一条规则`)
          }
        }

        return true
      }),
    body('changePieceWage')
      .if(body('type').equals(WageType.CHANGE_PIECE_WAGE))
      .isFloat({ min: 0 })
      .withMessage('换片单价不能为负数'),
    body('changeLineWage')
      .if(body('type').equals(WageType.CHANGE_LINE_WAGE))
      .isFloat({ min: 0 })
      .withMessage('换线单价不能为负数')
  ],

// 创建工价配置验证规则
export const createWageConfigSchema = Joi.object({
  type: Joi.number()
    .valid(...Object.values(WageType).filter(v => typeof v === 'number'))
    .required()
    .messages({
      'any.only': '工价类型必须是有效值(1-花样工价, 2-换片单价, 3-换线单价)',
      'any.required': '工价类型是必填项'
    }),

  patternId: Joi.when('type', {
    is: WageType.PATTERN_WAGE,
    then: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': '花样ID必须是数字',
        'number.integer': '花样ID必须是整数',
        'number.positive': '花样ID必须是正数',
        'any.required': '花样工价类型必须选择花样'
      }),
    otherwise: Joi.forbidden().messages({
      'any.unknown': '非花样工价类型不能设置花样ID'
    })
  }),

  patternWage: Joi.when('type', {
    is: WageType.PATTERN_WAGE,
    then: Joi.number()
      .min(0)
      .precision(2)
      .when('incentiveWage', {
        is: Joi.array().length(0).optional(),
        then: Joi.required().messages({
          'any.required': '花样工价类型必须设置花样工价或激励工价'
        }),
        otherwise: Joi.optional()
      })
      .messages({
        'number.base': '花样工价必须是数字',
        'number.min': '花样工价不能为负数',
        'number.precision': '花样工价最多保留2位小数'
      }),
    otherwise: Joi.forbidden().messages({
      'any.unknown': '非花样工价类型不能设置花样工价'
    })
  }),

  incentiveWage: Joi.when('type', {
    is: WageType.PATTERN_WAGE,
    then: incentiveWageArraySchema.optional(),
    otherwise: Joi.forbidden().messages({
      'any.unknown': '非花样工价类型不能设置激励工价'
    })
  }),

  changePieceWage: Joi.when('type', {
    is: WageType.CHANGE_PIECE_WAGE,
    then: Joi.number()
      .min(0)
      .precision(2)
      .required()
      .messages({
        'number.base': '换片单价必须是数字',
        'number.min': '换片单价不能为负数',
        'number.precision': '换片单价最多保留2位小数',
        'any.required': '换片单价类型必须设置换片单价'
      }),
    otherwise: Joi.forbidden().messages({
      'any.unknown': '非换片单价类型不能设置换片单价'
    })
  }),

  changeLineWage: Joi.when('type', {
    is: WageType.CHANGE_LINE_WAGE,
    then: Joi.number()
      .min(0)
      .precision(2)
      .required()
      .messages({
        'number.base': '换线单价必须是数字',
        'number.min': '换线单价不能为负数',
        'number.precision': '换线单价最多保留2位小数',
        'any.required': '换线单价类型必须设置换线单价'
      }),
    otherwise: Joi.forbidden().messages({
      'any.unknown': '非换线单价类型不能设置换线单价'
    })
  })
})

// 更新工价配置验证规则
export const updateWageConfigSchema = createWageConfigSchema.fork(
  ['type'],
  (schema) => schema.optional()
)

// 查询参数验证规则
export const wageConfigQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码必须大于0'
    }),

  pageSize: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量必须大于0',
      'number.max': '每页数量不能超过100'
    }),

  search: Joi.string()
    .trim()
    .max(100)
    .optional()
    .messages({
      'string.base': '搜索关键词必须是字符串',
      'string.max': '搜索关键词长度不能超过100个字符'
    }),

  type: Joi.number()
    .valid(...Object.values(WageType).filter(v => typeof v === 'number'))
    .optional()
    .messages({
      'any.only': '工价类型必须是有效值(1-花样工价, 2-换片单价, 3-换线单价)'
    }),

  patternId: Joi.number()
    .integer()
    .positive()
    .optional()
    .messages({
      'number.base': '花样ID必须是数字',
      'number.integer': '花样ID必须是整数',
      'number.positive': '花样ID必须是正数'
    }),

  createdUserId: Joi.number()
    .integer()
    .positive()
    .optional()
    .messages({
      'number.base': '创建人ID必须是数字',
      'number.integer': '创建人ID必须是整数',
      'number.positive': '创建人ID必须是正数'
    })
})

// ID参数验证规则
export const idParamSchema = Joi.object({
  id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'ID必须是数字',
      'number.integer': 'ID必须是整数',
      'number.positive': 'ID必须是正数',
      'any.required': 'ID是必填项'
    })
})

// 业务规则验证函数
export class WageConfigBusinessValidator {
  /**
   * 验证花样工价配置的唯一性
   * @param enterpriseId 企业ID
   * @param patternId 花样ID
   * @param excludeId 排除的工价配置ID（用于更新时）
   */
  static async validatePatternWageUniqueness(
    enterpriseId: number,
    patternId: number,
    excludeId?: number
  ): Promise<void> {
    const { WageConfig } = await import('./wageConfig.model')
    
    const whereClause: any = {
      enterpriseId,
      type: WageType.PATTERN_WAGE,
      patternId
    }

    if (excludeId) {
      whereClause.id = { [require('sequelize').Op.ne]: excludeId }
    }

    const existingConfig = await WageConfig.findOne({
      where: whereClause
    })

    if (existingConfig) {
      throw new Error('该花样已存在工价配置，请勿重复创建')
    }
  }

  /**
   * 验证换片单价配置的唯一性
   * @param enterpriseId 企业ID
   * @param excludeId 排除的工价配置ID（用于更新时）
   */
  static async validateChangePieceWageUniqueness(
    enterpriseId: number,
    excludeId?: number
  ): Promise<void> {
    const { WageConfig } = await import('./wageConfig.model')
    
    const whereClause: any = {
      enterpriseId,
      type: WageType.CHANGE_PIECE_WAGE
    }

    if (excludeId) {
      whereClause.id = { [require('sequelize').Op.ne]: excludeId }
    }

    const existingConfig = await WageConfig.findOne({
      where: whereClause
    })

    if (existingConfig) {
      throw new Error('企业已存在换片单价配置，请勿重复创建')
    }
  }

  /**
   * 验证换线单价配置的唯一性
   * @param enterpriseId 企业ID
   * @param excludeId 排除的工价配置ID（用于更新时）
   */
  static async validateChangeLineWageUniqueness(
    enterpriseId: number,
    excludeId?: number
  ): Promise<void> {
    const { WageConfig } = await import('./wageConfig.model')
    
    const whereClause: any = {
      enterpriseId,
      type: WageType.CHANGE_LINE_WAGE
    }

    if (excludeId) {
      whereClause.id = { [require('sequelize').Op.ne]: excludeId }
    }

    const existingConfig = await WageConfig.findOne({
      where: whereClause
    })

    if (existingConfig) {
      throw new Error('企业已存在换线单价配置，请勿重复创建')
    }
  }

  /**
   * 验证花样是否存在且属于当前企业
   * @param patternId 花样ID
   * @param enterpriseId 企业ID
   */
  static async validatePatternExists(
    patternId: number,
    enterpriseId: number
  ): Promise<void> {
    const { Pattern } = await import('../pattern/pattern.model')
    
    const pattern = await Pattern.findOne({
      where: {
        id: patternId,
        enterpriseId
      }
    })

    if (!pattern) {
      throw new Error('指定的花样不存在或不属于当前企业')
    }
  }

  /**
   * 验证工价配置是否可以删除
   * @param wageConfigId 工价配置ID
   * @param enterpriseId 企业ID
   */
  static async validateCanDelete(
    wageConfigId: number,
    enterpriseId: number
  ): Promise<void> {
    // 这里可以添加检查是否有关联的工资记录等业务逻辑
    // 目前暂时允许删除，后续可以根据业务需求添加限制
    
    // 示例：检查是否有关联的工资记录
    // const { SalaryRecord } = await import('../salary/salaryRecord.model')
    // const relatedRecords = await SalaryRecord.count({
    //   where: {
    //     wageConfigId,
    //     enterpriseId
    //   }
    // })
    
    // if (relatedRecords > 0) {
    //   throw new Error('该工价配置已被使用，无法删除')
    // }
  }
}
