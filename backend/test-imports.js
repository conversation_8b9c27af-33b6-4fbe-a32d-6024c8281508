// 简单的导入测试
console.log('开始测试导入...');

try {
  console.log('测试基础模块导入...');
  require('express');
  console.log('✓ Express 导入成功');
  
  require('sequelize');
  console.log('✓ Sequelize 导入成功');
  
  require('joi');
  console.log('✓ Joi 导入成功');
  
  console.log('测试项目模块导入...');
  
  // 测试模型导入
  const { WageConfig } = require('./src/shared/database/models/WageConfig');
  console.log('✓ WageConfig 模型导入成功');
  
  // 测试验证模块导入
  const validation = require('./src/modules/wageConfig/wageConfig.validation');
  console.log('✓ WageConfig 验证模块导入成功');
  
  // 测试服务模块导入
  const service = require('./src/modules/wageConfig/wageConfig.service');
  console.log('✓ WageConfig 服务模块导入成功');
  
  // 测试控制器模块导入
  const controller = require('./src/modules/wageConfig/wageConfig.controller');
  console.log('✓ WageConfig 控制器模块导入成功');
  
  // 测试路由模块导入
  const routes = require('./src/modules/wageConfig/wageConfig.routes');
  console.log('✓ WageConfig 路由模块导入成功');
  
  console.log('所有导入测试通过！');
  
} catch (error) {
  console.error('导入测试失败:', error.message);
  console.error('错误堆栈:', error.stack);
}
