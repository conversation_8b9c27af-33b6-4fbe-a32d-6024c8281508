// TypeScript 导入测试
console.log('开始测试 TypeScript 导入...');

try {
  console.log('测试基础模块导入...');
  
  // 测试模型导入
  import('./src/shared/database/models/WageConfig').then(() => {
    console.log('✓ WageConfig 模型导入成功');
  }).catch((error) => {
    console.error('✗ WageConfig 模型导入失败:', error.message);
  });
  
  // 测试验证模块导入
  import('./src/modules/wageConfig/wageConfig.validation').then(() => {
    console.log('✓ WageConfig 验证模块导入成功');
  }).catch((error) => {
    console.error('✗ WageConfig 验证模块导入失败:', error.message);
  });
  
} catch (error) {
  console.error('导入测试失败:', error);
}
